# Complete cPanel Deployment Guide for Interlock AI Website

## Prerequisites

- cPanel hosting account with Node.js support (version 18.x or higher)
- Access to cPanel File Manager
- Domain configured in cPanel
- Neon PostgreSQL database (already configured)

## Step 1: Prepare Your Application

### Run the Deployment Script
```bash
node deploy-cpanel.js
```

This script will:
- ✅ Install dependencies
- ✅ Build the application
- ✅ Copy all necessary files to `cpanel-deployment/` folder
- ✅ Create production-ready package.json
- ✅ Generate environment template
- ✅ Create server.js for cPanel
- ✅ Install production dependencies

## Step 2: Upload to cPanel

### 2.1 Create ZIP Archive
1. Navigate to the `cpanel-deployment/` folder
2. Select all contents (not the folder itself)
3. Create a ZIP archive named `interlock-ai-app.zip`

### 2.2 Upload via cPanel File Manager
1. Log into your cPanel
2. Open **File Manager**
3. Navigate to `public_html/` (or your domain's directory)
4. Click **Upload**
5. Select and upload `interlock-ai-app.zip`
6. After upload, right-click the ZIP file and select **Extract**
7. Delete the ZIP file after extraction

## Step 3: Configure Environment Variables

### 3.1 Create .env File
1. In File Manager, locate `.env.template`
2. Right-click and select **Copy**
3. Rename the copy to `.env`
4. Right-click `.env` and select **Edit**

### 3.2 Update Environment Variables
```env
# Database Configuration (Your Neon PostgreSQL)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# Admin Credentials (CHANGE THESE!)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password-123"

# Next.js Configuration
NEXTAUTH_SECRET="your-random-secret-key-here"
NEXTAUTH_URL="https://yourdomain.com"

# Optional: Server Actions Encryption
NEXT_SERVER_ACTIONS_ENCRYPTION_KEY="another-random-key-here"
```

**⚠️ IMPORTANT:** Change the admin credentials for security!

## Step 4: Set Up Node.js Application

### 4.1 Access Node.js Interface
1. In cPanel, find and click **Node.js**
2. Click **Create Application**

### 4.2 Configure Application Settings
- **Node.js Version:** 18.x or higher
- **Application Mode:** Production
- **Application Root:** `/public_html` (or your upload directory)
- **Application URL:** Your domain (e.g., `yourdomain.com`)
- **Application Startup File:** `server.js`

### 4.3 Set Environment Variables in cPanel
In the Node.js interface, add these environment variables:
- `NODE_ENV` = `production`
- `DATABASE_URL` = (your Neon database URL)
- `ADMIN_EMAIL` = (your admin email)
- `ADMIN_PASSWORD` = (your admin password)

## Step 5: Database Setup

### 5.1 Generate Prisma Client
In the Node.js terminal (or SSH):
```bash
npx prisma generate
```

### 5.2 Deploy Database Schema
```bash
npx prisma db push
```

### 5.3 Seed Database (Optional)
If you have a seed file:
```bash
npx prisma db seed
```

## Step 6: Start the Application

### 6.1 Install Dependencies (if needed)
```bash
npm install --production
```

### 6.2 Start the Application
1. In the Node.js interface, click **Restart**
2. Wait for the application to start
3. Check the status - it should show "Running"

## Step 7: Configure Domain (if needed)

### 7.1 For Main Domain
If deploying to your main domain, the app should be accessible immediately.

### 7.2 For Subdomain
1. Create a subdomain in cPanel
2. Point it to your application directory
3. Update `NEXTAUTH_URL` in environment variables

## Step 8: Verify Deployment

### 8.1 Test Website Access
1. Visit your domain in a browser
2. Check that the homepage loads correctly
3. Test navigation between pages

### 8.2 Test Admin Panel
1. Go to `yourdomain.com/admin`
2. Log in with your admin credentials
3. Verify the CMS functionality works

### 8.3 Test Database Connection
1. Check that dynamic content loads
2. Try creating/editing content in the admin panel
3. Verify changes appear on the frontend

## Troubleshooting

### Common Issues and Solutions

#### 1. Application Won't Start
- Check Node.js version (must be 18.x+)
- Verify all files were uploaded correctly
- Check error logs in cPanel

#### 2. Database Connection Errors
- Verify `DATABASE_URL` is correct
- Ensure Neon database is accessible
- Check if Prisma client is generated

#### 3. Environment Variables Not Working
- Ensure `.env` file exists and is readable
- Check that variables are also set in cPanel Node.js interface
- Restart the application after changes

#### 4. Static Files Not Loading
- Check file permissions (should be 644 for files, 755 for directories)
- Verify `public/` folder was uploaded correctly
- Check Next.js configuration

#### 5. Admin Panel Access Issues
- Verify admin credentials in environment variables
- Check that `/admin` route is accessible
- Ensure authentication is working

### Log Files
Check these locations for error logs:
- cPanel Error Logs
- Node.js application logs
- Browser developer console

## Security Checklist

- [ ] Changed default admin credentials
- [ ] Set strong passwords
- [ ] Configured HTTPS (SSL certificate)
- [ ] Set secure environment variables
- [ ] Restricted file permissions
- [ ] Updated database connection security

## Performance Optimization

### 8.1 Enable Compression
Add to your `.htaccess` file:
```apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### 8.2 Cache Headers
```apache
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

## Maintenance

### Regular Tasks
1. **Monitor application logs** for errors
2. **Update dependencies** periodically
3. **Backup database** regularly
4. **Monitor performance** and optimize as needed
5. **Update content** through the admin panel

### Updates
To update your application:
1. Run the deployment script again locally
2. Upload new files to cPanel
3. Restart the Node.js application
4. Test functionality

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review cPanel documentation for Node.js
3. Contact your hosting provider for Node.js support
4. Check Next.js deployment documentation

---

**🎉 Congratulations!** Your Interlock AI website should now be live on cPanel hosting!
