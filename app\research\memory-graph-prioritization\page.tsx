"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { OptimizedImage } from "@/components/optimized-image"

export default function MemoryGraphPrioritizationPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Memory Graph Prioritization
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Developing algorithms that prioritize information based on relevance, recency, and emotional significance.
        </p>

        <div className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)] mb-12">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
          <div className="absolute inset-0 z-0">
            <OptimizedImage
              src="/placeholder.svg?height=600&width=800"
              alt="Memory Graph Prioritization"
              width={800}
              height={600}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Overview</h2>
            <p className="text-gray-300 mb-4">
              Memory Graph Prioritization is an innovative approach to information management in AI systems that mimics
              how human memory prioritizes and organizes information. Our research focuses on developing algorithms that
              can dynamically adjust the importance of stored information based on factors like relevance to current
              tasks, recency of acquisition, and emotional significance.
            </p>
            <p className="text-gray-300">
              By modeling the prioritization mechanisms of human memory, we're creating AI systems that can more
              effectively manage large volumes of information, focusing computational resources on the most important
              data points while allowing less relevant information to fade into the background, similar to how human
              memory works.
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Key Research Areas</h2>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Dynamic Memory Graphs</h3>
                  <p className="text-gray-300">
                    Creating graph-based memory structures that can reorganize themselves based on changing priorities
                    and contexts.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Emotional Significance Modeling</h3>
                  <p className="text-gray-300">
                    Developing algorithms that can assign emotional significance to information, influencing its
                    priority in memory systems.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Contextual Relevance Algorithms</h3>
                  <p className="text-gray-300">
                    Creating systems that can dynamically assess the relevance of stored information to current tasks
                    and contexts.
                  </p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Team</h2>
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold mr-4">
                MC
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Dr. Marcus Chen</h3>
                <p className="text-gray-300">Lead Researcher, Memory Graph Prioritization</p>
              </div>
            </div>
            <p className="text-gray-300">
              Our interdisciplinary team combines expertise in cognitive psychology, computer science, and neuroscience
              to develop memory systems that reflect human memory prioritization. We collaborate with experts in human
              memory research to ensure our models accurately capture the nuanced ways humans prioritize information.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
