#!/bin/bash

echo "========================================"
echo "  Interlock AI - cPanel Deployment (No node_modules)"
echo "========================================"
echo

echo "[1/3] Building application..."
npm run build
if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo
echo "[2/3] Preparing deployment files..."
node deploy-cpanel-no-modules.js
if [ $? -ne 0 ]; then
    echo "ERROR: Deployment preparation failed"
    exit 1
fi

echo
echo "[3/3] Creating ZIP archive..."
if [ -f "interlock-ai-deployment-no-modules.zip" ]; then
    rm "interlock-ai-deployment-no-modules.zip"
fi

cd cpanel-deployment-no-modules
zip -r ../interlock-ai-deployment-no-modules.zip .
cd ..

echo
echo "========================================"
echo "  DEPLOYMENT READY!"
echo "========================================"
echo
echo "Files prepared in: cpanel-deployment-no-modules/"
echo "ZIP archive created: interlock-ai-deployment-no-modules.zip"
echo
echo "Next steps:"
echo "1. Upload interlock-ai-deployment-no-modules.zip to your cPanel"
echo "2. Extract it in public_html directory"
echo "3. Run 'npm install --production' on the server"
echo "4. Run 'npx prisma generate' on the server"
echo "5. Follow DEPLOYMENT_INSTRUCTIONS.md"
echo
echo "Opening deployment guide..."
if command -v xdg-open > /dev/null; then
    xdg-open cpanel-deployment-no-modules/DEPLOYMENT_INSTRUCTIONS.md
elif command -v open > /dev/null; then
    open cpanel-deployment-no-modules/DEPLOYMENT_INSTRUCTIONS.md
else
    echo "Please manually open cpanel-deployment-no-modules/DEPLOYMENT_INSTRUCTIONS.md"
fi
