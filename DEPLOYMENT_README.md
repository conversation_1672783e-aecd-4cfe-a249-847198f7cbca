# Interlock AI - cPanel Deployment Files

This directory contains all the necessary files and scripts to deploy your Interlock AI Next.js application to cPanel hosting.

## Quick Start

### Option 1: Automated Script (Recommended)
**Windows:**
```bash
quick-deploy.bat
```

**Mac/Linux:**
```bash
chmod +x quick-deploy.sh
./quick-deploy.sh
```

### Option 2: Manual Steps
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Prepare deployment files
npm run deploy:prepare

# Or run all at once
npm run deploy:full
```

## Files Overview

| File | Purpose |
|------|---------|
| `deploy-cpanel.js` | Main deployment preparation script |
| `CPANEL_DEPLOYMENT_GUIDE.md` | Complete step-by-step deployment guide |
| `DEPLOYMENT_CHECKLIST.md` | Comprehensive checklist for deployment |
| `quick-deploy.bat` | Windows automated deployment script |
| `quick-deploy.sh` | Mac/Linux automated deployment script |
| `package-scripts.json` | Additional npm scripts for deployment |

## What the Deployment Script Does

1. **Cleans** previous deployment directory
2. **Installs** all dependencies
3. **Builds** the Next.js application
4. **Copies** all necessary files to `cpanel-deployment/`
5. **Creates** production-ready `package.json`
6. **Installs** production dependencies
7. **Generates** environment template
8. **Creates** `server.js` for cPanel
9. **Provides** deployment instructions

## Output Structure

After running the deployment script, you'll have:

```
cpanel-deployment/
├── .next/                 # Built application
├── app/                   # Next.js app directory
├── components/            # React components
├── lib/                   # Utility libraries
├── public/                # Static assets
├── prisma/                # Database schema
├── node_modules/          # Production dependencies
├── package.json           # Production package.json
├── server.js              # cPanel server file
├── .env.template          # Environment variables template
└── DEPLOYMENT_INSTRUCTIONS.md
```

## Environment Variables Required

```env
DATABASE_URL="your-neon-postgresql-url"
ADMIN_EMAIL="your-admin-email"
ADMIN_PASSWORD="your-secure-password"
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="https://yourdomain.com"
```

## cPanel Requirements

- **Node.js:** Version 18.x or higher
- **Memory:** At least 512MB RAM
- **Storage:** At least 1GB free space
- **Database:** PostgreSQL (Neon) access
- **SSL:** Recommended for production

## Deployment Steps Summary

1. **Prepare:** Run deployment script
2. **Upload:** ZIP and upload to cPanel
3. **Configure:** Set up Node.js app in cPanel
4. **Environment:** Create `.env` file with your settings
5. **Database:** Deploy Prisma schema
6. **Launch:** Start the application

## Troubleshooting

### Common Issues

**Build Fails:**
- Check Node.js version (18.x+)
- Ensure all dependencies are installed
- Review build errors in console

**Upload Issues:**
- Verify ZIP file is not corrupted
- Check cPanel file size limits
- Ensure sufficient disk space

**App Won't Start:**
- Check Node.js version in cPanel
- Verify environment variables
- Review error logs

**Database Errors:**
- Confirm DATABASE_URL is correct
- Check Neon database accessibility
- Verify Prisma schema is deployed

## Support

1. **Read the full guide:** `CPANEL_DEPLOYMENT_GUIDE.md`
2. **Use the checklist:** `DEPLOYMENT_CHECKLIST.md`
3. **Check logs:** cPanel error logs and application logs
4. **Contact support:** Your hosting provider for cPanel-specific issues

## Security Notes

- ⚠️ **Change default admin credentials**
- ⚠️ **Use strong passwords**
- ⚠️ **Enable HTTPS/SSL**
- ⚠️ **Keep environment variables secure**
- ⚠️ **Regular security updates**

## Next Steps After Deployment

1. **Test all functionality**
2. **Set up monitoring**
3. **Configure backups**
4. **Train content administrators**
5. **Plan maintenance schedule**

---

**Need help?** Check the comprehensive guides included in this deployment package!
