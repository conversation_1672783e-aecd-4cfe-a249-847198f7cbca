@echo off
echo ========================================
echo   Interlock AI - cPanel Deployment
echo ========================================
echo.

echo [1/4] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/4] Building application...
call npm run build
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo [3/4] Preparing deployment files...
call node deploy-cpanel.js
if errorlevel 1 (
    echo ERROR: Deployment preparation failed
    pause
    exit /b 1
)

echo.
echo [4/4] Creating ZIP archive...
if exist "interlock-ai-deployment.zip" del "interlock-ai-deployment.zip"
powershell -command "Compress-Archive -Path 'cpanel-deployment\*' -DestinationPath 'interlock-ai-deployment.zip'"

echo.
echo ========================================
echo   DEPLOYMENT READY!
echo ========================================
echo.
echo Files prepared in: cpanel-deployment\
echo ZIP archive created: interlock-ai-deployment.zip
echo.
echo Next steps:
echo 1. Upload interlock-ai-deployment.zip to your cPanel
echo 2. Extract it in public_html directory
echo 3. Follow CPANEL_DEPLOYMENT_GUIDE.md
echo.
echo Press any key to open the deployment guide...
pause >nul
start CPANEL_DEPLOYMENT_GUIDE.md
