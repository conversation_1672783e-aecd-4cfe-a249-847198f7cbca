#!/bin/bash

echo "========================================"
echo "  Interlock AI - cPanel Deployment"
echo "========================================"
echo

echo "[1/4] Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "[2/4] Building application..."
npm run build
if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    exit 1
fi

echo
echo "[3/4] Preparing deployment files..."
node deploy-cpanel.js
if [ $? -ne 0 ]; then
    echo "ERROR: Deployment preparation failed"
    exit 1
fi

echo
echo "[4/4] Creating ZIP archive..."
if [ -f "interlock-ai-deployment.zip" ]; then
    rm "interlock-ai-deployment.zip"
fi

cd cpanel-deployment
zip -r ../interlock-ai-deployment.zip .
cd ..

echo
echo "========================================"
echo "  DEPLOYMENT READY!"
echo "========================================"
echo
echo "Files prepared in: cpanel-deployment/"
echo "ZIP archive created: interlock-ai-deployment.zip"
echo
echo "Next steps:"
echo "1. Upload interlock-ai-deployment.zip to your cPanel"
echo "2. Extract it in public_html directory"
echo "3. Follow CPANEL_DEPLOYMENT_GUIDE.md"
echo
echo "Opening deployment guide..."
if command -v xdg-open > /dev/null; then
    xdg-open CPANEL_DEPLOYMENT_GUIDE.md
elif command -v open > /dev/null; then
    open CPANEL_DEPLOYMENT_GUIDE.md
else
    echo "Please manually open CPANEL_DEPLOYMENT_GUIDE.md"
fi
