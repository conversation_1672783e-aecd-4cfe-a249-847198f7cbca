# PowerShell script to exclude problematic directories from OneDrive syncing
# This helps prevent file access conflicts during Next.js development

Write-Host "Setting up OneDrive exclusions for Next.js development..." -ForegroundColor Green

# Get the current directory
$currentDir = Get-Location

# Directories to exclude from OneDrive syncing
$excludeDirs = @(
    "node_modules",
    ".next",
    "deployment-packages"
)

foreach ($dir in $excludeDirs) {
    $fullPath = Join-Path $currentDir $dir
    
    if (Test-Path $fullPath) {
        Write-Host "Excluding $dir from OneDrive syncing..." -ForegroundColor Yellow
        
        # Set the directory attribute to exclude from OneDrive
        try {
            attrib +U "$fullPath" /S /D
            Write-Host "✅ Successfully excluded $dir" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Failed to exclude $dir : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "Directory $dir does not exist, skipping..." -ForegroundColor Gray
    }
}

Write-Host "`nOneDrive exclusion setup complete!" -ForegroundColor Green
Write-Host "Note: You may need to restart OneDrive for changes to take effect." -ForegroundColor Yellow
