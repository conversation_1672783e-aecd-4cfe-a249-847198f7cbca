@echo off
echo ========================================
echo   Interlock AI - cPanel Deployment (No node_modules)
echo ========================================
echo.

echo [1/3] Building application...
call npm run build
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo [2/3] Preparing deployment files...
call node deploy-cpanel-no-modules.js
if errorlevel 1 (
    echo ERROR: Deployment preparation failed
    pause
    exit /b 1
)

echo.
echo [3/3] Creating ZIP archive...
if exist "interlock-ai-deployment-no-modules.zip" del "interlock-ai-deployment-no-modules.zip"
powershell -command "Compress-Archive -Path 'cpanel-deployment-no-modules\*' -DestinationPath 'interlock-ai-deployment-no-modules.zip'"

echo.
echo ========================================
echo   DEPLOYMENT READY!
echo ========================================
echo.
echo Files prepared in: cpanel-deployment-no-modules\
echo ZIP archive created: interlock-ai-deployment-no-modules.zip
echo.
echo Next steps:
echo 1. Upload interlock-ai-deployment-no-modules.zip to your cPanel
echo 2. Extract it in public_html directory
echo 3. Run 'npm install --production' on the server
echo 4. Run 'npx prisma generate' on the server
echo 5. Follow DEPLOYMENT_INSTRUCTIONS.md
echo.
echo Press any key to open the deployment guide...
pause >nul
start cpanel-deployment-no-modules\DEPLOYMENT_INSTRUCTIONS.md
