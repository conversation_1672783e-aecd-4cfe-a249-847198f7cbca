# cPanel Deployment Checklist

## Pre-Deployment Preparation

### Local Environment
- [ ] All code changes committed and tested locally
- [ ] Dependencies are up to date (`npm install`)
- [ ] Application builds successfully (`npm run build`)
- [ ] Database schema is finalized
- [ ] Environment variables are documented

### cPanel Requirements
- [ ] cPanel hosting account with Node.js support
- [ ] Node.js version 18.x or higher available
- [ ] Domain/subdomain configured
- [ ] SSL certificate installed (recommended)
- [ ] Database access confirmed (Neon PostgreSQL)

## Deployment Process

### Step 1: Prepare Files
- [ ] Run deployment script: `node deploy-cpanel.js` or `npm run deploy:prepare`
- [ ] Verify `cpanel-deployment/` folder created
- [ ] Check all required files are present
- [ ] Create ZIP archive of deployment files

### Step 2: Upload to cPanel
- [ ] Log into cPanel
- [ ] Access File Manager
- [ ] Navigate to correct directory (`public_html/`)
- [ ] Upload ZIP file
- [ ] Extract ZIP file
- [ ] Delete ZIP file after extraction

### Step 3: Environment Configuration
- [ ] Copy `.env.template` to `.env`
- [ ] Update `DATABASE_URL` with correct Neon connection string
- [ ] Set secure `ADMIN_EMAIL` and `ADMIN_PASSWORD`
- [ ] Generate and set `NEXTAUTH_SECRET`
- [ ] Update `NEXTAUTH_URL` with your domain
- [ ] Set file permissions (644 for files, 755 for directories)

### Step 4: Node.js Application Setup
- [ ] Access Node.js interface in cPanel
- [ ] Create new Node.js application
- [ ] Set Node.js version (18.x+)
- [ ] Configure application root directory
- [ ] Set application URL
- [ ] Set startup file to `server.js`
- [ ] Add environment variables in cPanel interface

### Step 5: Database Setup
- [ ] Generate Prisma client: `npx prisma generate`
- [ ] Deploy database schema: `npx prisma db push`
- [ ] Seed database if needed: `npx prisma db seed`
- [ ] Verify database connection

### Step 6: Application Launch
- [ ] Install production dependencies: `npm install --production`
- [ ] Start the application (click "Restart" in cPanel)
- [ ] Verify application status shows "Running"
- [ ] Check for any startup errors in logs

## Post-Deployment Verification

### Website Functionality
- [ ] Homepage loads correctly
- [ ] All pages are accessible
- [ ] Navigation works properly
- [ ] Images and static assets load
- [ ] Responsive design works on mobile
- [ ] Contact form functions (if applicable)

### Admin Panel Testing
- [ ] Admin login page accessible (`/admin`)
- [ ] Can log in with admin credentials
- [ ] CMS interface loads properly
- [ ] Can create/edit content
- [ ] Changes reflect on frontend
- [ ] Image uploads work (if implemented)

### Database Operations
- [ ] Dynamic content displays correctly
- [ ] Database queries execute successfully
- [ ] Data persistence works
- [ ] No database connection errors

### Performance & Security
- [ ] Page load times are acceptable
- [ ] HTTPS is working (SSL certificate)
- [ ] Admin credentials are secure
- [ ] Environment variables are not exposed
- [ ] Error pages display properly (404, 500)

## Troubleshooting Checklist

### If Application Won't Start
- [ ] Check Node.js version compatibility
- [ ] Verify all files uploaded correctly
- [ ] Check startup file path (`server.js`)
- [ ] Review error logs in cPanel
- [ ] Ensure environment variables are set

### If Database Errors Occur
- [ ] Verify `DATABASE_URL` format and credentials
- [ ] Check Neon database is accessible
- [ ] Confirm Prisma client is generated
- [ ] Test database connection manually
- [ ] Check for schema mismatches

### If Pages Don't Load
- [ ] Verify Next.js build completed successfully
- [ ] Check static file permissions
- [ ] Ensure `.next` folder was uploaded
- [ ] Review routing configuration
- [ ] Check for missing dependencies

### If Admin Panel Issues
- [ ] Confirm admin credentials in environment
- [ ] Check authentication middleware
- [ ] Verify admin routes are accessible
- [ ] Test login functionality
- [ ] Check for JavaScript errors in browser console

## Security Verification

### Credentials & Access
- [ ] Default admin password changed
- [ ] Strong passwords implemented
- [ ] Environment variables secured
- [ ] Database credentials protected
- [ ] No sensitive data in client-side code

### File Permissions
- [ ] Application files have correct permissions
- [ ] `.env` file is not publicly accessible
- [ ] Database files are secured
- [ ] Upload directories have proper restrictions

### Network Security
- [ ] HTTPS enabled and working
- [ ] SSL certificate valid
- [ ] Database connections encrypted
- [ ] No exposed API endpoints without authentication

## Performance Optimization

### Caching & Compression
- [ ] Enable gzip compression in cPanel
- [ ] Set appropriate cache headers
- [ ] Optimize images and static assets
- [ ] Configure CDN if available

### Monitoring
- [ ] Set up error logging
- [ ] Monitor application performance
- [ ] Check resource usage
- [ ] Set up uptime monitoring

## Maintenance Tasks

### Regular Maintenance
- [ ] Monitor application logs weekly
- [ ] Update dependencies monthly
- [ ] Backup database regularly
- [ ] Review security settings quarterly
- [ ] Test backup restoration process

### Content Management
- [ ] Train content administrators
- [ ] Document content update procedures
- [ ] Set up content approval workflow
- [ ] Create content backup schedule

## Emergency Procedures

### If Site Goes Down
- [ ] Check cPanel application status
- [ ] Review recent changes
- [ ] Check error logs
- [ ] Restart Node.js application
- [ ] Contact hosting support if needed

### Rollback Plan
- [ ] Keep previous deployment ZIP as backup
- [ ] Document rollback procedure
- [ ] Test rollback process
- [ ] Maintain database backups for rollback

---

## Final Sign-off

**Deployment completed by:** ________________  
**Date:** ________________  
**Domain:** ________________  
**All checks passed:** [ ] Yes [ ] No  

**Notes:**
_________________________________
_________________________________
_________________________________

**Next review date:** ________________
