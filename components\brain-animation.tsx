"use client"

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

interface NeuralSpark {
  id: number
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  opacity: number
  size: number
  connections: number[]
}

export function BrainAnimation() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sparksRef = useRef<NeuralSpark[]>([])
  const animationRef = useRef<number>()

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Function to spawn a spark from outside the screen toward the logo
    const spawnSpark = (targetX: number, targetY: number) => {
      // Choose a random edge to spawn from
      const edge = Math.floor(Math.random() * 4) // 0: top, 1: right, 2: bottom, 3: left
      let startX, startY

      const margin = 100 // Distance outside the screen

      switch (edge) {
        case 0: // Top edge
          startX = Math.random() * canvas.width
          startY = -margin
          break
        case 1: // Right edge
          startX = canvas.width + margin
          startY = Math.random() * canvas.height
          break
        case 2: // Bottom edge
          startX = Math.random() * canvas.width
          startY = canvas.height + margin
          break
        case 3: // Left edge
          startX = -margin
          startY = Math.random() * canvas.height
          break
        default:
          startX = 0
          startY = 0
      }

      // Calculate direction toward the logo center
      const dx = targetX - startX
      const dy = targetY - startY
      const distance = Math.sqrt(dx * dx + dy * dy)
      const speed = 1.5 + Math.random() * 1.0

      return {
        id: Math.random(),
        x: startX,
        y: startY,
        vx: (dx / distance) * speed,
        vy: (dy / distance) * speed,
        life: 0,
        maxLife: Math.floor(distance / speed) + 20, // Life based on travel time
        opacity: 0.8 + Math.random() * 0.2,
        size: 1 + Math.random() * 2,
        connections: [],
        targetX: targetX,
        targetY: targetY
      }
    }

    // Initialize neural sparks
    const initSparks = () => {
      sparksRef.current = []
      const centerX = canvas.width * 0.5
      const centerY = canvas.height * 0.4

      for (let i = 0; i < 40; i++) {
        sparksRef.current.push(spawnSpark(centerX, centerY))
      }
    }
    initSparks()

    // Function to draw the Interlock logo as background
    const drawInterlockLogo = (centerX: number, centerY: number, scale: number, opacity: number) => {
      const logoScale = scale * 5 // Make logo slightly larger than brain
      const logoOpacity = opacity * 0.30 // Very transparent

      // Save context state
      ctx.save()
      ctx.globalAlpha = logoOpacity

      // Draw circuit board pattern background (simplified)
      ctx.strokeStyle = `rgba(26, 58, 92, ${logoOpacity * 2})`
      ctx.lineWidth = 1

      // Horizontal lines
      const lines = [
        { x1: centerX - logoScale * 0.8, y1: centerY - logoScale * 0.3, x2: centerX - logoScale * 0.2, y2: centerY - logoScale * 0.3 },
        { x1: centerX + logoScale * 0.1, y1: centerY - logoScale * 0.3, x2: centerX + logoScale * 0.8, y2: centerY - logoScale * 0.3 },
        { x1: centerX - logoScale * 0.8, y1: centerY - logoScale * 0.1, x2: centerX - logoScale * 0.4, y2: centerY - logoScale * 0.1 },
        { x1: centerX - logoScale * 0.2, y1: centerY - logoScale * 0.1, x2: centerX + logoScale * 0.4, y2: centerY - logoScale * 0.1 },
        { x1: centerX + logoScale * 0.6, y1: centerY - logoScale * 0.1, x2: centerX + logoScale * 0.8, y2: centerY - logoScale * 0.1 },
        { x1: centerX - logoScale * 0.8, y1: centerY + logoScale * 0.1, x2: centerX - logoScale * 0.3, y2: centerY + logoScale * 0.1 },
        { x1: centerX - logoScale * 0.1, y1: centerY + logoScale * 0.1, x2: centerX + logoScale * 0.3, y2: centerY + logoScale * 0.1 },
        { x1: centerX + logoScale * 0.5, y1: centerY + logoScale * 0.1, x2: centerX + logoScale * 0.8, y2: centerY + logoScale * 0.1 },
        { x1: centerX - logoScale * 0.8, y1: centerY + logoScale * 0.3, x2: centerX - logoScale * 0.1, y2: centerY + logoScale * 0.3 },
        { x1: centerX + logoScale * 0.2, y1: centerY + logoScale * 0.3, x2: centerX + logoScale * 0.8, y2: centerY + logoScale * 0.3 }
      ]

      lines.forEach(line => {
        ctx.beginPath()
        ctx.moveTo(line.x1, line.y1)
        ctx.lineTo(line.x2, line.y2)
        ctx.stroke()
      })

      // Vertical lines
      const verticalLines = [
        { x: centerX - logoScale * 0.5, y1: centerY - logoScale * 0.4, y2: centerY - logoScale * 0.2 },
        { x: centerX - logoScale * 0.5, y1: centerY - logoScale * 0.05, y2: centerY + logoScale * 0.4 },
        { x: centerX - logoScale * 0.2, y1: centerY - logoScale * 0.4, y2: centerY - logoScale * 0.15 },
        { x: centerX - logoScale * 0.2, y1: centerY + logoScale * 0.05, y2: centerY + logoScale * 0.4 },
        { x: centerX + logoScale * 0.1, y1: centerY - logoScale * 0.4, y2: centerY - logoScale * 0.05 },
        { x: centerX + logoScale * 0.1, y1: centerY + logoScale * 0.15, y2: centerY + logoScale * 0.4 },
        { x: centerX + logoScale * 0.4, y1: centerY - logoScale * 0.4, y2: centerY - logoScale * 0.1 },
        { x: centerX + logoScale * 0.4, y1: centerY + logoScale * 0.05, y2: centerY + logoScale * 0.4 },
        { x: centerX + logoScale * 0.6, y1: centerY - logoScale * 0.4, y2: centerY + logoScale * 0.1 },
        { x: centerX + logoScale * 0.6, y1: centerY + logoScale * 0.3, y2: centerY + logoScale * 0.4 }
      ]

      verticalLines.forEach(line => {
        ctx.beginPath()
        ctx.moveTo(line.x, line.y1)
        ctx.lineTo(line.x, line.y2)
        ctx.stroke()
      })

      // Draw glowing connection points
      const connectionPoints = [
        { x: centerX - logoScale * 0.4, y: centerY - logoScale * 0.1, color: '#00ffff' },
        { x: centerX - logoScale * 0.1, y: centerY - logoScale * 0.3, color: '#ff00ff' },
        { x: centerX + logoScale * 0.2, y: centerY - logoScale * 0.05, color: '#00ffff' },
        { x: centerX + logoScale * 0.3, y: centerY + logoScale * 0.3, color: '#ff00ff' },
        { x: centerX + logoScale * 0.6, y: centerY - logoScale * 0.1, color: '#00ffff' },
        { x: centerX - logoScale * 0.2, y: centerY - logoScale * 0.1, color: '#ff00ff' },
        { x: centerX + logoScale * 0.1, y: centerY + logoScale * 0.15, color: '#00ffff' },
        { x: centerX + logoScale * 0.5, y: centerY + logoScale * 0.15, color: '#ff00ff' }
      ]

      connectionPoints.forEach(point => {
        ctx.fillStyle = point.color
        ctx.shadowColor = point.color
        ctx.shadowBlur = 8
        ctx.beginPath()
        ctx.arc(point.x, point.y, 2, 0, Math.PI * 2)
        ctx.fill()
      })
      ctx.shadowBlur = 0

      // Draw the main INTERLOCK text
      ctx.font = `bold ${logoScale * 0.12}px Arial, sans-serif`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'

      // Text gradient effect (simplified for canvas)
      const gradient = ctx.createLinearGradient(centerX - logoScale * 0.4, centerY, centerX + logoScale * 0.4, centerY)
      gradient.addColorStop(0, '#00ffff')
      gradient.addColorStop(0.5, '#00ccff')
      gradient.addColorStop(1, '#0099ff')

      ctx.fillStyle = gradient
      ctx.shadowColor = '#00ffff'
      ctx.shadowBlur = 15
      ctx.fillText('INTERLOCK', centerX, centerY)
      ctx.shadowBlur = 0

      // Restore context state
      ctx.restore()
    }

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw brain outline with cyberpunk glow effect
      const centerX = canvas.width * 0.5
      const centerY = canvas.height * 0.4
      const scale = Math.min(canvas.width, canvas.height) * 0.35
      const time = Date.now() * 0.002

      // Draw the Interlock logo as background
      const logoOpacity = 0.8 + Math.sin(time * 0.5) * 0.2
      drawInterlockLogo(centerX, centerY, scale, logoOpacity)

      // Brain outline removed - keeping only the neural network animations

      // Update neural sparks
      sparksRef.current.forEach((spark) => {
        // Move spark toward the logo
        spark.x += spark.vx
        spark.y += spark.vy
        spark.life++

        // Calculate distance to target (logo center)
        const distanceToTarget = Math.sqrt(
          Math.pow(spark.x - centerX, 2) + Math.pow(spark.y - centerY, 2)
        )

        // Fade in as spark approaches, then fade out near the end
        const fadeInDistance = 200
        const fadeOutDistance = 50

        if (distanceToTarget > fadeInDistance) {
          // Fade in from distance
          spark.opacity = Math.min(0.8, (400 - distanceToTarget) / 200)
        } else if (distanceToTarget < fadeOutDistance) {
          // Fade out as it approaches the logo
          spark.opacity = Math.max(0, distanceToTarget / fadeOutDistance * 0.8)
        } else {
          // Full opacity in the middle range
          spark.opacity = 0.8 + Math.random() * 0.2
        }

        // Reset spark if it reaches the logo or is too old
        if (distanceToTarget < 30 || spark.life >= spark.maxLife) {
          // Respawn from outside the screen toward the logo
          const newSpark = spawnSpark(centerX, centerY)
          spark.x = newSpark.x
          spark.y = newSpark.y
          spark.vx = newSpark.vx
          spark.vy = newSpark.vy
          spark.life = 0
          spark.maxLife = newSpark.maxLife
          spark.opacity = 0
          spark.size = newSpark.size
        }
      })

      // Draw neural network connections between nearby sparks
      ctx.strokeStyle = 'rgba(0, 255, 255, 0.3)'
      ctx.lineWidth = 1
      sparksRef.current.forEach((spark, index) => {
        sparksRef.current.slice(index + 1).forEach(otherSpark => {
          const distance = Math.sqrt(
            Math.pow(spark.x - otherSpark.x, 2) +
            Math.pow(spark.y - otherSpark.y, 2)
          )

          if (distance < 100 && spark.opacity > 0.3 && otherSpark.opacity > 0.3) {
            ctx.beginPath()
            ctx.moveTo(spark.x, spark.y)
            ctx.lineTo(otherSpark.x, otherSpark.y)
            const connectionOpacity = (100 - distance) / 100 * 0.4 * Math.min(spark.opacity, otherSpark.opacity)
            ctx.globalAlpha = connectionOpacity
            ctx.stroke()
            ctx.globalAlpha = 1
          }
        })
      })

      // Circuit patterns within brain removed - keeping only neural network

      // Draw neural sparks
      sparksRef.current.forEach(spark => {
        if (spark.opacity > 0) {
          // Main spark
          ctx.fillStyle = `rgba(0, 255, 255, ${spark.opacity})`
          ctx.beginPath()
          ctx.arc(spark.x, spark.y, spark.size, 0, Math.PI * 2)
          ctx.fill()

          // Glow effect
          ctx.shadowColor = '#00ffff'
          ctx.shadowBlur = 8
          ctx.fillStyle = `rgba(0, 255, 255, ${spark.opacity * 0.3})`
          ctx.beginPath()
          ctx.arc(spark.x, spark.y, spark.size * 3, 0, Math.PI * 2)
          ctx.fill()
          ctx.shadowBlur = 0
        }
      })

      // Draw pulsing center
      const pulseTime = Date.now() * 0.003
      const pulseSize = 8 + Math.sin(pulseTime) * 4
      const pulseOpacity = 0.5 + Math.sin(pulseTime * 2) * 0.3

      ctx.fillStyle = `rgba(124, 58, 237, ${pulseOpacity})`
      ctx.shadowColor = '#7c3aed'
      ctx.shadowBlur = 20
      ctx.beginPath()
      ctx.arc(centerX, centerY, pulseSize, 0, Math.PI * 2)
      ctx.fill()
      ctx.shadowBlur = 0

      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <canvas
        ref={canvasRef}
        className="absolute inset-0 opacity-40"
        style={{ mixBlendMode: 'screen' }}
      />

      {/* Additional cyberpunk effects */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-transparent to-cyan-900/10"
        animate={{
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Neural grid overlay */}
      <motion.div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
        animate={{
          backgroundPosition: ['0px 0px', '50px 50px'],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Scanning lines effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-500/8 to-transparent h-24"
        animate={{
          y: ["-100%", "100vh"],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Vertical scanning line */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/6 to-transparent w-16"
        animate={{
          x: ["-100%", "100vw"],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Pulsing corner accents */}
      <motion.div
        className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-cyan-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-cyan-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          delay: 0.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-4 left-4 w-8 h-8 border-l-2 border-b-2 border-purple-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          delay: 1,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-4 right-4 w-8 h-8 border-r-2 border-b-2 border-purple-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          delay: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}
