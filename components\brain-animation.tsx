"use client"

import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'

interface NeuralSpark {
  id: number
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  opacity: number
  size: number
  connections: number[]
}

export function BrainAnimation() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sparksRef = useRef<NeuralSpark[]>([])
  const animationRef = useRef<number>()

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Detailed brain outline points (cyberpunk brain shape)
    const brainPoints = [
      // Frontal lobe
      { x: 0.15, y: 0.35 }, { x: 0.12, y: 0.3 }, { x: 0.1, y: 0.25 },
      { x: 0.12, y: 0.2 }, { x: 0.15, y: 0.15 }, { x: 0.2, y: 0.12 },
      { x: 0.25, y: 0.1 }, { x: 0.3, y: 0.08 }, { x: 0.35, y: 0.07 },
      { x: 0.4, y: 0.06 }, { x: 0.45, y: 0.05 }, { x: 0.5, y: 0.05 },
      // Top curve
      { x: 0.55, y: 0.05 }, { x: 0.6, y: 0.06 }, { x: 0.65, y: 0.07 },
      { x: 0.7, y: 0.08 }, { x: 0.75, y: 0.1 }, { x: 0.8, y: 0.12 },
      { x: 0.85, y: 0.15 }, { x: 0.88, y: 0.2 }, { x: 0.9, y: 0.25 },
      // Parietal lobe
      { x: 0.88, y: 0.3 }, { x: 0.85, y: 0.35 }, { x: 0.82, y: 0.4 },
      { x: 0.78, y: 0.45 }, { x: 0.74, y: 0.5 }, { x: 0.7, y: 0.55 },
      // Temporal lobe
      { x: 0.65, y: 0.6 }, { x: 0.6, y: 0.65 }, { x: 0.55, y: 0.68 },
      { x: 0.5, y: 0.7 }, { x: 0.45, y: 0.68 }, { x: 0.4, y: 0.65 },
      { x: 0.35, y: 0.6 }, { x: 0.3, y: 0.55 }, { x: 0.26, y: 0.5 },
      { x: 0.22, y: 0.45 }, { x: 0.18, y: 0.4 }
    ]

    // Circuit patterns within brain
    const circuitPaths = [
      // Main neural pathways
      [
        { x: 0.3, y: 0.3 }, { x: 0.4, y: 0.25 }, { x: 0.5, y: 0.3 },
        { x: 0.6, y: 0.35 }, { x: 0.7, y: 0.4 }
      ],
      [
        { x: 0.25, y: 0.4 }, { x: 0.35, y: 0.45 }, { x: 0.45, y: 0.4 },
        { x: 0.55, y: 0.45 }, { x: 0.65, y: 0.5 }
      ],
      [
        { x: 0.4, y: 0.2 }, { x: 0.45, y: 0.3 }, { x: 0.5, y: 0.4 },
        { x: 0.55, y: 0.5 }
      ]
    ]

    // Initialize neural sparks from brain outline
    const initSparks = () => {
      sparksRef.current = []
      const centerX = canvas.width * 0.5
      const centerY = canvas.height * 0.4
      const scale = Math.min(canvas.width, canvas.height) * 0.3

      for (let i = 0; i < 40; i++) {
        // Pick random point on brain outline
        const brainPoint = brainPoints[Math.floor(Math.random() * brainPoints.length)]
        const startX = centerX + (brainPoint.x - 0.5) * scale
        const startY = centerY + (brainPoint.y - 0.3) * scale

        // Random direction outward from brain center
        const angle = Math.atan2(startY - centerY, startX - centerX) + (Math.random() - 0.5) * 0.5
        const speed = 0.8 + Math.random() * 1.2

        sparksRef.current.push({
          id: i,
          x: startX,
          y: startY,
          vx: Math.cos(angle) * speed,
          vy: Math.sin(angle) * speed,
          life: 0,
          maxLife: 120 + Math.random() * 80,
          opacity: 0.8 + Math.random() * 0.2,
          size: 1 + Math.random() * 2,
          connections: []
        })
      }
    }
    initSparks()

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw brain outline with cyberpunk glow effect
      const centerX = canvas.width * 0.5
      const centerY = canvas.height * 0.4
      const scale = Math.min(canvas.width, canvas.height) * 0.35
      const time = Date.now() * 0.002

      // Multiple glow layers for cyberpunk effect
      for (let i = 0; i < 3; i++) {
        const glowIntensity = 0.05 + i * 0.02
        const glowSize = 12 - i * 3

        ctx.strokeStyle = `rgba(0, 255, 255, ${glowIntensity})`
        ctx.lineWidth = glowSize
        ctx.shadowColor = '#00ffff'
        ctx.shadowBlur = 25 - i * 5

        ctx.beginPath()
        brainPoints.forEach((point, index) => {
          const wobble = Math.sin(time + index * 0.3) * (2 - i * 0.5)
          const x = centerX + (point.x - 0.5) * scale + wobble
          const y = centerY + (point.y - 0.3) * scale + Math.cos(time + index * 0.2) * (1 - i * 0.3)

          if (index === 0) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        })
        ctx.closePath()
        ctx.stroke()
      }
      ctx.shadowBlur = 0

      // Main brain outline with pulsing effect
      const pulseIntensity = 0.6 + Math.sin(time * 1.5) * 0.3
      ctx.strokeStyle = `rgba(0, 255, 255, ${pulseIntensity})`
      ctx.lineWidth = 2.5

      ctx.beginPath()
      brainPoints.forEach((point, index) => {
        const x = centerX + (point.x - 0.5) * scale
        const y = centerY + (point.y - 0.3) * scale

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.closePath()
      ctx.stroke()

      // Secondary brain outline with purple accent
      ctx.strokeStyle = `rgba(147, 51, 234, ${0.4 + Math.sin(time * 2) * 0.2})`
      ctx.lineWidth = 1.5

      ctx.beginPath()
      brainPoints.forEach((point, index) => {
        const x = centerX + (point.x - 0.5) * scale * 0.95
        const y = centerY + (point.y - 0.3) * scale * 0.95

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.closePath()
      ctx.stroke()

      // Inner brain details
      ctx.strokeStyle = 'rgba(124, 58, 237, 0.3)'
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(centerX - scale * 0.1, centerY)
      ctx.quadraticCurveTo(centerX, centerY - scale * 0.1, centerX + scale * 0.1, centerY)
      ctx.stroke()

      // Update neural sparks
      sparksRef.current.forEach((spark, index) => {
        // Move spark outward
        spark.x += spark.vx
        spark.y += spark.vy
        spark.life++

        // Fade out as spark ages
        spark.opacity = Math.max(0, 1 - (spark.life / spark.maxLife))

        // Reset spark if it's too old or off screen
        if (spark.life >= spark.maxLife ||
          spark.x < -50 || spark.x > canvas.width + 50 ||
          spark.y < -50 || spark.y > canvas.height + 50) {

          // Respawn from brain outline
          const brainPoint = brainPoints[Math.floor(Math.random() * brainPoints.length)]
          const startX = centerX + (brainPoint.x - 0.5) * scale
          const startY = centerY + (brainPoint.y - 0.3) * scale
          const angle = Math.atan2(startY - centerY, startX - centerX) + (Math.random() - 0.5) * 0.5
          const speed = 0.8 + Math.random() * 1.2

          spark.x = startX
          spark.y = startY
          spark.vx = Math.cos(angle) * speed
          spark.vy = Math.sin(angle) * speed
          spark.life = 0
          spark.opacity = 0.8 + Math.random() * 0.2
        }
      })

      // Draw neural network connections between nearby sparks
      ctx.strokeStyle = 'rgba(0, 255, 255, 0.3)'
      ctx.lineWidth = 1
      sparksRef.current.forEach((spark, index) => {
        sparksRef.current.slice(index + 1).forEach(otherSpark => {
          const distance = Math.sqrt(
            Math.pow(spark.x - otherSpark.x, 2) +
            Math.pow(spark.y - otherSpark.y, 2)
          )

          if (distance < 100 && spark.opacity > 0.3 && otherSpark.opacity > 0.3) {
            ctx.beginPath()
            ctx.moveTo(spark.x, spark.y)
            ctx.lineTo(otherSpark.x, otherSpark.y)
            const connectionOpacity = (100 - distance) / 100 * 0.4 * Math.min(spark.opacity, otherSpark.opacity)
            ctx.globalAlpha = connectionOpacity
            ctx.stroke()
            ctx.globalAlpha = 1
          }
        })
      })

      // Draw circuit patterns within brain
      ctx.strokeStyle = 'rgba(147, 51, 234, 0.4)'
      ctx.lineWidth = 1.5
      circuitPaths.forEach((path, pathIndex) => {
        const pathTime = time + pathIndex * 0.5
        const pathOpacity = 0.3 + Math.sin(pathTime) * 0.2
        ctx.globalAlpha = pathOpacity

        ctx.beginPath()
        path.forEach((point, index) => {
          const x = centerX + (point.x - 0.5) * scale
          const y = centerY + (point.y - 0.3) * scale

          if (index === 0) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        })
        ctx.stroke()
        ctx.globalAlpha = 1
      })

      // Draw neural sparks
      sparksRef.current.forEach(spark => {
        if (spark.opacity > 0) {
          // Main spark
          ctx.fillStyle = `rgba(0, 255, 255, ${spark.opacity})`
          ctx.beginPath()
          ctx.arc(spark.x, spark.y, spark.size, 0, Math.PI * 2)
          ctx.fill()

          // Glow effect
          ctx.shadowColor = '#00ffff'
          ctx.shadowBlur = 8
          ctx.fillStyle = `rgba(0, 255, 255, ${spark.opacity * 0.3})`
          ctx.beginPath()
          ctx.arc(spark.x, spark.y, spark.size * 3, 0, Math.PI * 2)
          ctx.fill()
          ctx.shadowBlur = 0
        }
      })

      // Draw pulsing center
      const pulseTime = Date.now() * 0.003
      const pulseSize = 8 + Math.sin(pulseTime) * 4
      const pulseOpacity = 0.5 + Math.sin(pulseTime * 2) * 0.3

      ctx.fillStyle = `rgba(124, 58, 237, ${pulseOpacity})`
      ctx.shadowColor = '#7c3aed'
      ctx.shadowBlur = 20
      ctx.beginPath()
      ctx.arc(centerX, centerY, pulseSize, 0, Math.PI * 2)
      ctx.fill()
      ctx.shadowBlur = 0

      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <canvas
        ref={canvasRef}
        className="absolute inset-0 opacity-40"
        style={{ mixBlendMode: 'screen' }}
      />

      {/* Additional cyberpunk effects */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-transparent to-cyan-900/10"
        animate={{
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Neural grid overlay */}
      <motion.div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
        animate={{
          backgroundPosition: ['0px 0px', '50px 50px'],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Scanning lines effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-500/8 to-transparent h-24"
        animate={{
          y: ["-100%", "100vh"],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Vertical scanning line */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/6 to-transparent w-16"
        animate={{
          x: ["-100%", "100vw"],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Pulsing corner accents */}
      <motion.div
        className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-cyan-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-cyan-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          delay: 0.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-4 left-4 w-8 h-8 border-l-2 border-b-2 border-purple-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          delay: 1,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-4 right-4 w-8 h-8 border-r-2 border-b-2 border-purple-400/40"
        animate={{
          opacity: [0.4, 1, 0.4],
        }}
        transition={{
          duration: 2,
          delay: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}
