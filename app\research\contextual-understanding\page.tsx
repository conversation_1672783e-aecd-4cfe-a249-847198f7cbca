"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { OptimizedImage } from "@/components/optimized-image"

export default function ContextualUnderstandingPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Contextual Understanding
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Building systems that comprehend nuanced contexts and adapt responses accordingly.
        </p>

        <div className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)] mb-12">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
          <div className="absolute inset-0 z-0">
            <OptimizedImage
              src="/placeholder.svg?height=600&width=800"
              alt="Contextual Understanding"
              width={800}
              height={600}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Overview</h2>
            <p className="text-gray-300 mb-4">
              Contextual Understanding research focuses on developing AI systems that can comprehend and adapt to the
              nuanced contexts in which information exists and interactions occur. Unlike traditional AI approaches that
              often treat data in isolation, our research aims to create systems that understand the broader context,
              including cultural references, situational factors, and implicit knowledge.
            </p>
            <p className="text-gray-300">
              By enhancing AI's ability to understand context, we're developing systems that can provide more
              appropriate, helpful, and human-like responses across a wide range of applications, from conversational
              agents to decision support systems.
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Key Research Areas</h2>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Situational Context Modeling</h3>
                  <p className="text-gray-300">
                    Developing frameworks that can understand and adapt to different situational contexts, including
                    physical environments and social settings.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Cultural Context Integration</h3>
                  <p className="text-gray-300">
                    Creating systems that can understand and appropriately respond to cultural contexts, including
                    idioms, references, and norms.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Implicit Knowledge Representation</h3>
                  <p className="text-gray-300">
                    Developing methods to represent and utilize the implicit knowledge that humans often rely on for
                    contextual understanding.
                  </p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Team</h2>
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold mr-4">
                SR
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Dr. Sophia Rodriguez</h3>
                <p className="text-gray-300">Lead Researcher, Contextual Understanding</p>
              </div>
            </div>
            <p className="text-gray-300">
              Our team brings together expertise in linguistics, anthropology, psychology, and computer science to
              develop AI systems with enhanced contextual understanding. We collaborate with researchers across
              disciplines to ensure our models can comprehend and adapt to the rich contexts that characterize human
              communication and decision-making.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
