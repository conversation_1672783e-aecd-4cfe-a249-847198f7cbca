@echo off
echo ========================================
echo   Prisma Client Fix Script
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
  echo X Error: package.json not found in current directory
  echo Please run this script from your application root directory
  pause
  exit /b 1
)

REM Check if prisma schema exists
if not exist "prisma\schema.prisma" (
  echo X Error: prisma\schema.prisma not found
  echo Please make sure your Prisma schema file exists
  pause
  exit /b 1
)

echo Looking for Prisma installation...
where npx >nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo X Error: npx command not found
  echo Please make sure Node.js is installed correctly
  pause
  exit /b 1
)

echo Cleaning up any existing Prisma client...
if exist "node_modules\.prisma" rd /s /q "node_modules\.prisma"
if exist "node_modules\@prisma\client" rd /s /q "node_modules\@prisma\client"

echo Reinstalling Prisma client...
call npm install @prisma/client

echo Generating Prisma client...
call npx prisma generate

if %ERRORLEVEL% equ 0 (
  echo Prisma client generated successfully!
) else (
  echo X Error generating Prisma client
  echo Trying with additional options...
  
  echo Creating .npmrc with legacy-peer-deps...
  echo legacy-peer-deps=true > .npmrc
  
  echo Setting Prisma binary targets...
  set PRISMA_CLI_BINARY_TARGETS=linux-openssl-1.1.x
  set PRISMA_ENGINES_MIRROR=https://binaries.prisma.sh
  
  echo Trying again with binary targets set...
  call npx prisma generate
  
  if %ERRORLEVEL% equ 0 (
    echo Prisma client generated successfully with binary targets!
  ) else (
    echo X Error generating Prisma client even with binary targets
    echo Please check your database connection and Prisma schema
    pause
    exit /b 1
  )
)

echo.
echo ========================================
echo   Prisma Client Fix Complete!
echo ========================================
echo.
echo If you still encounter issues, please check:
echo 1. Your database connection string in .env
echo 2. That your database is accessible from this server
echo 3. That your Prisma schema matches your database structure
echo.
echo For more help, see PRISMA_TROUBLESHOOTING.md
echo.
pause
