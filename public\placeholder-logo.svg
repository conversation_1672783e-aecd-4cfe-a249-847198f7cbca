<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="120" viewBox="0 0 400 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Glowing effect filters -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="textGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Gradient for the text -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00ccff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099ff;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Dark background -->
  <rect width="400" height="120" fill="#0a0a0f"/>

  <!-- Circuit board pattern background -->
  <!-- Horizontal lines -->
  <line x1="0" y1="20" x2="120" y2="20" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="150" y1="20" x2="400" y2="20" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="0" y1="40" x2="80" y2="40" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="110" y1="40" x2="290" y2="40" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="320" y1="40" x2="400" y2="40" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="0" y1="80" x2="100" y2="80" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="130" y1="80" x2="270" y2="80" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="300" y1="80" x2="400" y2="80" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="0" y1="100" x2="140" y2="100" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="170" y1="100" x2="400" y2="100" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>

  <!-- Vertical lines -->
  <line x1="60" y1="0" x2="60" y2="50" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="60" y1="70" x2="60" y2="120" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="120" y1="0" x2="120" y2="30" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="120" y1="50" x2="120" y2="120" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="180" y1="0" x2="180" y2="60" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="180" y1="80" x2="180" y2="120" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="240" y1="0" x2="240" y2="40" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="240" y1="60" x2="240" y2="120" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="300" y1="0" x2="300" y2="80" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="300" y1="100" x2="300" y2="120" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>
  <line x1="340" y1="0" x2="340" y2="120" stroke="#1a3a5c" stroke-width="1" opacity="0.6"/>

  <!-- Corner connections -->
  <path d="M 60 20 L 80 20 L 80 40" stroke="#1a3a5c" stroke-width="1" fill="none" opacity="0.6"/>
  <path d="M 120 20 L 140 20 L 140 40 L 110 40" stroke="#1a3a5c" stroke-width="1" fill="none" opacity="0.6"/>
  <path d="M 180 40 L 200 40 L 200 60 L 180 60" stroke="#1a3a5c" stroke-width="1" fill="none" opacity="0.6"/>
  <path d="M 240 80 L 270 80 L 270 100 L 240 100" stroke="#1a3a5c" stroke-width="1" fill="none" opacity="0.6"/>
  <path d="M 300 40 L 320 40 L 320 60 L 340 60" stroke="#1a3a5c" stroke-width="1" fill="none" opacity="0.6"/>

  <!-- Glowing connection points -->
  <circle cx="80" cy="40" r="2" fill="#00ffff" filter="url(#glow)" opacity="0.8"/>
  <circle cx="140" cy="20" r="2" fill="#ff00ff" filter="url(#glow)" opacity="0.8"/>
  <circle cx="200" cy="60" r="2" fill="#00ffff" filter="url(#glow)" opacity="0.8"/>
  <circle cx="270" cy="100" r="2" fill="#ff00ff" filter="url(#glow)" opacity="0.8"/>
  <circle cx="320" cy="40" r="2" fill="#00ffff" filter="url(#glow)" opacity="0.8"/>
  <circle cx="110" cy="40" r="1.5" fill="#ff00ff" filter="url(#glow)" opacity="0.6"/>
  <circle cx="180" cy="80" r="1.5" fill="#00ffff" filter="url(#glow)" opacity="0.6"/>
  <circle cx="300" cy="80" r="1.5" fill="#ff00ff" filter="url(#glow)" opacity="0.6"/>

  <!-- Main INTERLOCK text -->
  <text x="200" y="70" font-family="Arial, sans-serif" font-size="32" font-weight="900" text-anchor="middle"
        fill="url(#textGradient)" filter="url(#textGlow)" letter-spacing="2px">
    INTERLOCK
  </text>
</svg>