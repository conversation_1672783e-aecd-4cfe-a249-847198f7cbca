# Fixing Prisma Memory Issues on cPanel

This guide addresses the "Out of memory: wasm memory" error when running Prisma on cPanel servers with limited memory resources.

## The Problem

When running `prisma generate` on cPanel, you might encounter this error:

```
RangeError: WebAssembly.Instance(): Out of memory: wasm memory
```

This happens because Prisma's schema engine uses WebAssembly (WASM) which requires significant memory to compile your schema. In shared hosting environments like cPanel, there are often strict memory limits that can cause this process to fail.

## Quick Solution

Run the included memory fix script:

```bash
node fix-prisma-memory.js
```

This script will:
1. Configure Prisma to use binary engines instead of WebAssembly
2. Set memory optimization parameters
3. Clean up any existing Prisma client
4. Reinstall and generate the Prisma client with reduced memory usage

## Manual Solution

If the script doesn't work, follow these steps manually:

### Step 1: Create .env file with binary engine configuration

Create or edit your `.env` file to include:

```
# Prisma configuration - use binary engine instead of WASM
PRISMA_SCHEMA_ENGINE_TYPE=binary
PRISMA_QUERY_ENGINE_TYPE=binary
PRISMA_CLI_QUERY_ENGINE_TYPE=binary

# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=512"
```

### Step 2: Clean up existing Prisma client

```bash
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma/client
```

### Step 3: Reinstall Prisma client

```bash
PRISMA_SCHEMA_ENGINE_TYPE=binary PRISMA_QUERY_ENGINE_TYPE=binary npm install @prisma/client
```

### Step 4: Generate Prisma client with reduced memory

```bash
PRISMA_SCHEMA_ENGINE_TYPE=binary PRISMA_QUERY_ENGINE_TYPE=binary NODE_OPTIONS="--max-old-space-size=512" npx prisma generate
```

## Alternative Solutions

If you're still experiencing memory issues, try these alternatives:

### Option 1: Generate Prisma client locally and upload

Generate the Prisma client on your local machine and upload the generated files to your server:

1. Run `npx prisma generate` locally
2. Upload the `node_modules/.prisma` and `node_modules/@prisma/client` directories to your server

### Option 2: Use a custom script to download pre-built binaries

Create a script that downloads the pre-built Prisma binaries for your platform instead of generating them:

```bash
#!/bin/bash
# Download pre-built Prisma binaries
mkdir -p node_modules/.prisma/client
curl -L https://binaries.prisma.sh/all_engines/[VERSION]/[PLATFORM]/query-engine.gz | gunzip > node_modules/.prisma/client/query-engine
chmod +x node_modules/.prisma/client/query-engine
```

Replace `[VERSION]` with your Prisma version and `[PLATFORM]` with your server platform.

### Option 3: Request a memory limit increase

Contact your hosting provider to request a memory limit increase for your Node.js application.

## Preventing Future Issues

To prevent these issues in future deployments:

1. Always include the binary engine configuration in your `.env` file
2. Use the `fix-prisma-memory.js` script after each deployment
3. Consider using a hosting provider with more generous memory limits for Node.js applications

## Need More Help?

If you continue to experience issues, please check the Prisma documentation or contact your hosting provider for assistance.
