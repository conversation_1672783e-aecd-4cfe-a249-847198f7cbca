# Interlock AI - cPanel Deployment Guide

## Quick Start

1. Upload all files to your cPanel hosting
2. Set up environment variables in .env file (copy from .env.template)
3. Run the memory fix script: `node fix-prisma-memory.js`
4. Install dependencies: `npm install --production`
5. Start the application using cPanel Node.js app interface

## Detailed Instructions

### 1. Prepare Your cPanel Environment

- Log in to your cPanel account
- Navigate to "Setup Node.js App" in the Software section
- Create a new Node.js application:
  - Application mode: Production
  - Node.js version: 20.x or higher
  - Application root: Your public_html or subdirectory
  - Application URL: Your domain or subdomain
  - Application startup file: server.js

### 2. Upload Files

- Upload all files from this package to your application directory
- Make sure to preserve the directory structure

### 3. Install Dependencies

- In cPanel terminal or SSH, navigate to your app directory
- Run: npm install --production

### 4. Environment Setup

- Copy .env.template to .env
- Edit .env with your actual database credentials and settings

### 5. Database Setup
- In cPanel terminal or SSH, navigate to your app directory
- Run: npx prisma generate (to generate Prisma client)
- Run: npx prisma db push (to sync database schema)
- Optional: npx prisma db seed (to seed initial data)

### 6. Start the Application
- Click "Restart" in the Node.js interface
- Your app should now be running!

## CloudLinux Specific Notes
- node_modules is managed by cPanel NodeJS Selector
- Do not upload node_modules directory
- Dependencies are installed in a virtual environment
- Use cPanel interface for package management

## Memory Issues

If you encounter memory errors when running Prisma commands, such as:
```
RangeError: WebAssembly.Instance(): Out of memory: wasm memory
```

Use the included memory fix script:
```bash
node fix-prisma-memory.js
```

This script configures Prisma to use binary engines instead of WebAssembly, which requires less memory. For detailed information, see the MEMORY_FIX_GUIDE.md file.

## Prisma Troubleshooting

If you encounter Prisma-related issues, this package includes several helpful scripts:

1. **fix-prisma-memory.js**: Fixes memory issues with Prisma
   - Run: `node fix-prisma-memory.js`

2. **server-fix-prisma.js**: Fixes Prisma client initialization issues
   - Run: `node server-fix-prisma.js`

3. **check-prisma-status.js**: Checks the status of your Prisma installation
   - Run: `node check-prisma-status.js`

4. **test-prisma-connection.js**: Tests the database connection
   - Run: `node test-prisma-connection.js`

For detailed Prisma troubleshooting, refer to these included guides:
- MEMORY_FIX_GUIDE.md: Fixing memory issues with Prisma
- PRISMA_TROUBLESHOOTING.md: Common issues and solutions
- CPANEL_PRISMA_GUIDE.md: Comprehensive guide for Prisma on cPanel
- PRISMA_FIX_INSTRUCTIONS.md: Step-by-step fix instructions

## General Troubleshooting

If you encounter any issues:

1. Check the application logs in cPanel
2. Verify your database connection string in .env
3. Make sure all required dependencies are installed
4. Ensure the Prisma client is generated properly
5. Check that your Node.js version is compatible (20.x+)

For more help, contact your hosting provider or refer to the Next.js documentation.
