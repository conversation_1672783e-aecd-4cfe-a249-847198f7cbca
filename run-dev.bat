@echo off
echo Starting Interlock AI Development Server...
echo.

REM Add Node.js to PATH
set PATH=%PATH%;C:\Program Files\nodejs

REM Change to project directory
cd /d "C:\Users\<USER>\OneDrive\Documents\Home\Website\Interlock-ai"

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found in PATH
    echo Please make sure Node.js is installed and try again
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm not found in PATH
    echo Please make sure npm is installed and try again
    pause
    exit /b 1
)

echo Node.js and npm found successfully!
echo.
echo Starting development server...
echo The app will be available at: http://localhost:3000
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the development server
npm run dev

pause
