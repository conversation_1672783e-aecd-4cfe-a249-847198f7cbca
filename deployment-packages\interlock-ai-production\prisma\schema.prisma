generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model authors {
  id            Int      @id @default(autoincrement())
  name          String
  bio           String
  genre         String
  image_url     String?
  initials      String
  twitter_url   String?
  instagram_url String?
  facebook_url  String?
  website_url   String?
  created_at    DateTime @default(now()) @db.Timestamp(6)
  updated_at    DateTime @default(now()) @db.Timestamp(6)
  updated_by    Int
  avg_rating    Decimal? @db.Decimal(3, 1)
}

model books {
  id             Int       @id @default(autoincrement())
  title          String    @db.VarChar(255)
  author         String    @db.VarChar(255)
  description    String
  cover_image    String    @db.VarChar(255)
  isbn           String?   @db.VarChar(50)
  published      DateTime? @db.Timestamp(6)
  genre          String    @db.VarChar(100)
  is_featured    Boolean?  @default(false)
  is_free        Boolean?  @default(false)
  free_url       String?   @db.<PERSON>ar<PERSON>(255)
  amazon_url     String?   @db.Var<PERSON>har(255)
  is_new_release Boolean?  @default(false)
  created_at     DateTime  @default(now()) @db.Timestamp(6)
  formats        Json?
}

model books_read {
  id         Int      @id @default(autoincrement())
  user_id    Int
  book_id    Int
  created_at DateTime @default(now()) @db.Timestamp(6)
}

model chat_messages {
  id              Int       @id @default(autoincrement())
  user_id         Int
  username        String    @db.VarChar(255)
  message         String
  timestamp       DateTime? @default(now()) @db.Timestamp(6)
  emoji_reactions Json?     @default("{}")
}

model homepage_settings {
  id           Int       @id @default(autoincrement())
  key          String    @unique(map: "homepage_settings_key_unique") @db.VarChar(50)
  title        String    @db.VarChar(255)
  subtitle     String?   @db.VarChar(500)
  content      String?
  image_url    String?
  cta_text     String?   @db.VarChar(100)
  cta_link     String?   @db.VarChar(255)
  updated_at   DateTime  @default(now()) @db.Timestamp(6)
  updated_by   Int
  release_date DateTime? @db.Timestamp(6)
}

model news_tickers {
  id         Int       @id @default(autoincrement())
  content    String
  is_active  Boolean   @default(true)
  created_at DateTime? @default(now()) @db.Timestamp(6)
  updated_at DateTime? @default(now()) @db.Timestamp(6)
  updated_by Int
}

model reviews {
  id          Int      @id @default(autoincrement())
  book_id     Int
  user_id     Int
  rating      Decimal  @db.Decimal(2, 1)
  review      String?
  created_at  DateTime @default(now()) @db.Timestamp(6)
  is_approved Boolean  @default(false)
}

model sessions {
  sid    String   @id @db.VarChar
  sess   Json     @db.Json
  expire DateTime @db.Timestamp(6)

  @@index([expire], map: "IDX_sessions_expire")
}

model site_content {
  id          Int      @id @default(autoincrement())
  key         String   @unique(map: "site_content_key_unique") @db.VarChar(50)
  title       String   @db.VarChar(255)
  content     String
  updated_at  DateTime @default(now()) @db.Timestamp(6)
  updated_by  Int
  image_url   String?
  section     String?  @db.VarChar(50)
  order_index Int?     @default(0)
}

model subscribers {
  id          Int      @id @default(autoincrement())
  email       String   @unique(map: "subscribers_email_unique") @db.VarChar(255)
  created_at  DateTime @default(now()) @db.Timestamp(6)
  name        String?  @db.VarChar(255)
  preferences Json?
}

model testimonials {
  id         Int      @id @default(autoincrement())
  name       String   @db.VarChar(255)
  role       String   @db.VarChar(100)
  text       String
  avatar_url String?  @db.VarChar(255)
  is_active  Boolean? @default(true)
}

model users {
  id         Int      @id @default(autoincrement())
  username   String   @unique(map: "users_username_unique") @db.VarChar(255)
  password   String   @db.VarChar(255)
  email      String   @unique(map: "users_email_unique") @db.VarChar(255)
  name       String   @db.VarChar(255)
  role       String   @default("customer") @db.VarChar(50)
  created_at DateTime @default(now()) @db.Timestamp(6)
}

model wishlists {
  id         Int      @id @default(autoincrement())
  user_id    Int
  book_id    Int
  created_at DateTime @default(now()) @db.Timestamp(6)
}

// New models for Interlock AI website
model research_areas {
  id          Int      @id @default(autoincrement())
  title       String
  summary     String
  lead        String
  last_updated DateTime @default(now()) @updatedAt @db.Timestamp(6)
  status      String   @default("published") // published, in-progress, archived
  category    String
  created_at  DateTime @default(now()) @db.Timestamp(6)
}

model feature_cards {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  icon        String
  image       String?
  order_index Int      @default(1)
  created_at  DateTime @default(now()) @db.Timestamp(6)
  updated_at  DateTime @updatedAt @db.Timestamp(6)
}

model team_members {
  id         Int      @id @default(autoincrement())
  name       String
  position   String
  bio        String
  image      String?
  initials   String
  order_index Int     @default(1)
  is_active  Boolean  @default(true)
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @updatedAt @db.Timestamp(6)
}

model job_postings {
  id           Int      @id @default(autoincrement())
  title        String
  department   String
  location     String
  type         String
  description  String
  requirements String[] // Array of strings
  is_active    Boolean  @default(true)
  posted_date  DateTime @default(now()) @db.Timestamp(6)
  created_at   DateTime @default(now()) @db.Timestamp(6)
  updated_at   DateTime @updatedAt @db.Timestamp(6)
}

model company_info {
  id           Int      @id @default(autoincrement())
  section      String   @unique // mission, story, values, etc.
  title        String
  content      String
  image        String?
  order_index  Int      @default(1)
  last_updated DateTime @default(now()) @updatedAt @db.Timestamp(6)
  created_at   DateTime @default(now()) @db.Timestamp(6)
}

model page_content {
  id           Int      @id @default(autoincrement())
  page_id      String   // homepage, research, about, etc.
  section_id   String   // hero, features, about, etc.
  title        String
  content      String
  image        String?
  order_index  Int      @default(1)
  is_active    Boolean  @default(true)
  last_updated DateTime @default(now()) @updatedAt @db.Timestamp(6)
  created_at   DateTime @default(now()) @db.Timestamp(6)

  @@unique([page_id, section_id])
}
