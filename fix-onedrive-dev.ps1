# PowerShell script to temporarily move project outside OneDrive for development
# This is a workaround for OneDrive file system conflicts with Next.js

Write-Host "OneDrive Next.js Development Fix" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$currentDir = Get-Location
$projectName = "Interlock-ai"
$tempDevPath = "C:\temp-dev\$projectName"

Write-Host "`nThis script will:" -ForegroundColor Yellow
Write-Host "1. Create a temporary development copy outside OneDrive" -ForegroundColor White
Write-Host "2. Copy your project files (excluding node_modules)" -ForegroundColor White
Write-Host "3. Install dependencies in the temp location" -ForegroundColor White
Write-Host "4. Start the development server" -ForegroundColor White

$confirm = Read-Host "`nDo you want to proceed? (y/n)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Operation cancelled." -ForegroundColor Red
    exit
}

# Create temp directory
Write-Host "`nCreating temporary development directory..." -ForegroundColor Green
if (Test-Path $tempDevPath) {
    Write-Host "Removing existing temp directory..." -ForegroundColor Yellow
    Remove-Item $tempDevPath -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDevPath -Force | Out-Null

# Copy project files (excluding problematic directories)
Write-Host "Copying project files..." -ForegroundColor Green
$excludeDirs = @("node_modules", ".next", "deployment-packages")
$excludeFiles = @("*.zip")

Get-ChildItem -Path $currentDir | Where-Object {
    $_.Name -notin $excludeDirs -and 
    $_.Name -notlike "*.zip" -and
    $_.Name -ne "fix-onedrive-dev.ps1"
} | Copy-Item -Destination $tempDevPath -Recurse -Force

Write-Host "✅ Project files copied to: $tempDevPath" -ForegroundColor Green

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Open a new terminal/command prompt" -ForegroundColor White
Write-Host "2. Navigate to: $tempDevPath" -ForegroundColor White
Write-Host "3. Run: npm install" -ForegroundColor White
Write-Host "4. Run: npm run dev" -ForegroundColor White
Write-Host "`nThe development server should work without OneDrive conflicts." -ForegroundColor Green
Write-Host "`nWhen you're done developing, copy your changes back to the OneDrive location." -ForegroundColor Yellow

# Open the temp directory in Explorer
Write-Host "`nOpening temp directory in Explorer..." -ForegroundColor Green
Start-Process explorer.exe -ArgumentList $tempDevPath
