# Prisma on cPanel: Complete Guide

This guide provides comprehensive instructions for setting up and troubleshooting Prisma on cPanel hosting environments.

## Table of Contents

1. [Initial Setup](#initial-setup)
2. [Common Issues](#common-issues)
3. [Troubleshooting Steps](#troubleshooting-steps)
4. [CloudLinux Specific Notes](#cloudlinux-specific-notes)
5. [Advanced Configuration](#advanced-configuration)
6. [Performance Optimization](#performance-optimization)

## Initial Setup

### Step 1: Upload Your Application

Upload your application to cPanel without the `node_modules` folder. This is especially important for CloudLinux environments where dependencies are managed in a virtual environment.

### Step 2: Set Up Environment Variables

Create a `.env` file with your database connection string and other environment variables:

```bash
# Database connection
DATABASE_URL="postgresql://username:password@hostname:port/database?schema=public"

# Next.js
NODE_ENV=production

# Admin credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
```

### Step 3: Install Dependencies

In cPanel terminal or SSH, navigate to your application directory and run:

```bash
npm install --production
```

The `postinstall` script in your package.json will automatically run `prisma generate` after dependencies are installed.

### Step 4: Generate Prisma Client (if needed)

If the postinstall script didn't run or failed, manually generate the Prisma client:

```bash
npx prisma generate
```

### Step 5: Deploy Database Schema

If this is a new deployment, push your database schema:

```bash
npx prisma db push
```

### Step 6: Start Your Application

In cPanel, go to the Node.js section and restart your application.

## Common Issues

### 1. Prisma Client Not Initialized

**Error:**
```
Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
```

**Solution:**
Run the server-fix-prisma.js script:
```bash
node server-fix-prisma.js
```

### 2. Memory Limit Exceeded

**Error:**
```
FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory
```

**Solution:**
Add to your cPanel environment variables:
```bash
NODE_OPTIONS="--max-old-space-size=2048"
```

### 3. Binary Targets Issue

**Error:**
```
Error: Unable to find the query engine binary for ...
```

**Solution:**
Add to your .env file:
```bash
PRISMA_CLI_BINARY_TARGETS="linux-openssl-1.1.x"
PRISMA_ENGINES_MIRROR="https://binaries.prisma.sh"
```

### 4. Database Connection Issues

**Error:**
```
Error: P1001: Can't reach database server at ...
```

**Solution:**
- Verify your DATABASE_URL in .env
- Check that your database is accessible from the server
- Verify your database credentials
- Check if your database provider requires SSL

## Troubleshooting Steps

### Step 1: Check Logs

Check the application logs in cPanel for detailed error messages.

### Step 2: Verify Environment

Ensure your environment variables are correctly set:

```bash
cat .env
```

### Step 3: Check Prisma Client

Verify that the Prisma client is generated:

```bash
ls -la node_modules/.prisma/client/
```

### Step 4: Run Fix Script

Run the server-fix-prisma.js script:

```bash
node server-fix-prisma.js
```

### Step 5: Manual Generation

If the script fails, try manual steps:

```bash
# Clean up
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma/client

# Reinstall
npm install @prisma/client

# Generate with options
PRISMA_CLI_BINARY_TARGETS="linux-openssl-1.1.x" npx prisma generate
```

## CloudLinux Specific Notes

CloudLinux NodeJS Selector has some specific requirements:

1. **No node_modules in application root**: Dependencies are managed in a virtual environment
2. **Symlinked dependencies**: node_modules is symlinked from the virtual environment
3. **Package management**: Use cPanel interface for package management when possible
4. **Memory limits**: CloudLinux often has stricter memory limits

### CloudLinux Workarounds

1. **Use .npmrc file**: Create a .npmrc file with `legacy-peer-deps=true`
2. **Increase memory limit**: Add NODE_OPTIONS to environment variables
3. **Use binary targets**: Set PRISMA_CLI_BINARY_TARGETS in .env
4. **Skip Prisma operations**: If database is already set up, you can skip Prisma commands

## Advanced Configuration

### Custom Schema Path

If your schema is in a non-standard location:

```bash
npx prisma generate --schema=./custom/path/schema.prisma
```

### Multiple Databases

For multiple databases, use connection strings with different names:

```prisma
datasource db1 {
  provider = "postgresql"
  url      = env("DATABASE_URL_1")
}

datasource db2 {
  provider = "postgresql"
  url      = env("DATABASE_URL_2")
}
```

### Connection Pooling

For better performance, use connection pooling:

```
DATABASE_URL="postgresql://username:password@hostname:port/database?schema=public&connection_limit=5&pool_timeout=2"
```

## Performance Optimization

### 1. Optimize Queries

Use `select` and `include` to fetch only the data you need:

```javascript
const user = await prisma.user.findUnique({
  where: { id: 1 },
  select: { id: true, name: true }
});
```

### 2. Use Transactions

Wrap multiple operations in transactions:

```javascript
await prisma.$transaction([
  prisma.user.create({ data: { name: 'Alice' } }),
  prisma.post.create({ data: { title: 'Hello World' } })
]);
```

### 3. Batch Operations

Use `createMany` for batch operations:

```javascript
await prisma.user.createMany({
  data: [
    { name: 'Alice' },
    { name: 'Bob' }
  ]
});
```

### 4. Connection Pooling

Use connection pooling for better performance:

```
DATABASE_URL="postgresql://username:password@hostname:port/database?schema=public&connection_limit=5&pool_timeout=2"
```
