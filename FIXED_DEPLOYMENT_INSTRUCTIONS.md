# Fixed Deployment Package Instructions

This document provides instructions for deploying the fixed version of the Interlock AI application to cPanel.

## What's Been Fixed

1. **Symbolic Link Issue**: Updated Next.js configuration to prevent symbolic link issues during build
2. **Prisma Client Generation**: Ensured Prisma client is properly generated during build
3. **Deployment Scripts**: Enhanced deployment scripts with better error handling and troubleshooting tools

## Deployment Steps

### 1. Upload the Deployment Package

Upload the `interlock-ai-deployment-no-modules-fixed.zip` file to your cPanel and extract it in your application directory.

### 2. Set Up Environment Variables

Create a `.env` file with your database connection string and other environment variables:

```bash
# Database connection
DATABASE_URL="postgresql://username:password@hostname:port/database?schema=require"

# Next.js
NODE_ENV=production

# Admin credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
```

Make sure to replace the placeholders with your actual database credentials.

### 3. Install Dependencies

In cPanel terminal or SSH, navigate to your application directory and run:

```bash
npm install --production
```

The `postinstall` script in your package.json will automatically run `prisma generate` after dependencies are installed.

### 4. Generate Prisma Client (if needed)

If the postinstall script didn't run or failed, manually generate the Prisma client:

```bash
npx prisma generate
```

### 5. Start Your Application

In cPanel, go to the Node.js section and restart your application.

## Troubleshooting

If you encounter any issues with Prisma client initialization, run the server-fix-prisma.js script:

```bash
node server-fix-prisma.js
```

This script will:
1. Clean up any existing Prisma client
2. Reinstall the Prisma client
3. Generate the Prisma client with the correct configuration

You can also check the status of your Prisma installation:

```bash
node check-prisma-status.js
```

And test your database connection:

```bash
node test-prisma-connection.js
```

## Additional Resources

For more detailed information, refer to these included guides:
- PRISMA_TROUBLESHOOTING.md: Common issues and solutions for Prisma
- CPANEL_PRISMA_GUIDE.md: Comprehensive guide for using Prisma on cPanel
- PRISMA_FIX_INSTRUCTIONS.md: Step-by-step instructions for fixing Prisma issues
