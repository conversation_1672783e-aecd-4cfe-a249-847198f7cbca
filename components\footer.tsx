import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react"

export default function Footer() {
  return (
    <footer className="border-t border-purple-500/20 bg-background/50 backdrop-blur-sm py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center">
              <img
                src="/placeholder-logo.svg"
                alt="Interlock"
                className="h-8 w-auto"
              />
            </div>
            <p className="text-sm text-gray-400">
              Pioneering the next generation of AI through human-inspired cognitive models.
            </p>
            <div className="flex space-x-4">
              <Link href="https://github.com" className="text-gray-400 hover:text-cyan-400 transition-colors">
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </Link>
              <Link href="https://linkedin.com" className="text-gray-400 hover:text-cyan-400 transition-colors">
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </Link>
              <Link href="/rss" className="text-gray-400 hover:text-cyan-400 transition-colors">
                <Rss className="h-5 w-5" />
                <span className="sr-only">RSS</span>
              </Link>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-white mb-4">Research</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/research/neural-associative-modeling"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Neural Associative Modeling
                </Link>
              </li>
              <li>
                <Link
                  href="/research/memory-graph-prioritization"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Memory Graph Prioritization
                </Link>
              </li>
              <li>
                <Link
                  href="/research/contextual-understanding"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Contextual Understanding
                </Link>
              </li>
              <li>
                <Link
                  href="/research/emotional-intelligence"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Emotional Intelligence
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-medium text-white mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/company/about" className="text-sm text-gray-400 hover:text-cyan-400 transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/company/careers" className="text-sm text-gray-400 hover:text-cyan-400 transition-colors">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/company/press" className="text-sm text-gray-400 hover:text-cyan-400 transition-colors">
                  Press
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-gray-400 hover:text-cyan-400 transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-medium text-white mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/legal/privacy-policy"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/legal/terms-of-service"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link
                  href="/legal/cookie-policy"
                  className="text-sm text-gray-400 hover:text-cyan-400 transition-colors"
                >
                  Cookie Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-purple-500/20 relative">
          <div className="text-center text-sm text-gray-400">
            <p>© {new Date().getFullYear()} Interlock LLC. All rights reserved.</p>
          </div>

          {/* Admin Pi Icon - Bottom Right */}
          <Link
            href="/admin"
            className="absolute bottom-0 right-0 text-gray-600 hover:text-cyan-400 transition-colors duration-300 opacity-30 hover:opacity-100"
            title="Admin Access"
          >
            <Pi className="h-4 w-4" />
            <span className="sr-only">Admin Access</span>
          </Link>
        </div>
      </div>
    </footer>
  )
}
