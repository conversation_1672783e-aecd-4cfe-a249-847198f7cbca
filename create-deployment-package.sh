#!/bin/bash

# Create Deployment Package Script
# This script creates a complete deployment package for Interlock AI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PACKAGE_NAME="interlock-ai-deployment-$(date +%Y%m%d_%H%M%S)"
PACKAGE_DIR="./deployment-packages/$PACKAGE_NAME"
SOURCE_DIR="$(pwd)"

# Functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║              INTERLOCK AI DEPLOYMENT PACKAGE                ║
║                     Package Creator                         ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

log "Creating deployment package: $PACKAGE_NAME"

# Create package directory
log "Creating package directory..."
mkdir -p "$PACKAGE_DIR"
success "Package directory created: $PACKAGE_DIR"

# Copy application files
log "Copying application files..."
rsync -av --progress \
  --exclude 'node_modules' \
  --exclude '.next' \
  --exclude '.git' \
  --exclude 'deployment-packages' \
  --exclude '.env*' \
  --exclude '*.log' \
  --exclude 'logs' \
  --exclude 'backups' \
  "$SOURCE_DIR/" "$PACKAGE_DIR/app/"

success "Application files copied"

# Copy deployment scripts
log "Copying deployment configuration..."
cp -r "$SOURCE_DIR/deployment" "$PACKAGE_DIR/"
success "Deployment configuration copied"

# Create package info
log "Creating package information..."
cat > "$PACKAGE_DIR/PACKAGE_INFO.txt" << EOF
Interlock AI - Production Deployment Package
============================================

Package Name: $PACKAGE_NAME
Created: $(date)
Source: $SOURCE_DIR
Node.js Version Required: 18+

Contents:
- app/                  # Complete application source code
- deployment/           # Deployment scripts and configuration
- PACKAGE_INFO.txt     # This file
- INSTALL.md           # Installation instructions

Quick Start:
1. Upload this entire package to your server
2. Follow instructions in deployment/README.md
3. Run: chmod +x deployment/deploy.sh && ./deployment/deploy.sh

Support: <EMAIL>
EOF

# Create installation guide
log "Creating installation guide..."
cat > "$PACKAGE_DIR/INSTALL.md" << 'EOF'
# Quick Installation Guide

## 1. Upload Package
Upload this entire package to your cPanel hosting account.

## 2. Extract (if compressed)
If you received this as a zip file, extract it to your desired location.

## 3. Navigate to Deployment Directory
```bash
cd deployment/
```

## 4. Configure Environment
```bash
cp environment/.env.production environment/.env.production.local
# Edit the .env.production.local file with your actual values
```

## 5. Run Pre-Deployment Check
```bash
node pre-deployment-check.js
```

## 6. Deploy
```bash
chmod +x deploy.sh
./deploy.sh
```

## 7. Verify
Visit your domain and check that the application is running.

For detailed instructions, see `deployment/README.md`
EOF

# Create version info
log "Creating version information..."
if [ -f "$SOURCE_DIR/package.json" ]; then
    VERSION=$(grep '"version"' "$SOURCE_DIR/package.json" | cut -d'"' -f4)
    echo "Application Version: $VERSION" >> "$PACKAGE_DIR/PACKAGE_INFO.txt"
fi

# Make scripts executable
log "Setting script permissions..."
chmod +x "$PACKAGE_DIR/deployment/deploy.sh"
chmod +x "$PACKAGE_DIR/deployment/pre-deployment-check.js"
chmod +x "$PACKAGE_DIR/deployment/production-build.js"
chmod +x "$PACKAGE_DIR/deployment/scripts/"*.js
success "Script permissions set"

# Create compressed package
log "Creating compressed package..."
cd "$(dirname "$PACKAGE_DIR")"
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"
zip -r "$PACKAGE_NAME.zip" "$PACKAGE_NAME" > /dev/null
cd "$SOURCE_DIR"
success "Compressed packages created"

# Package summary
echo -e "\n${GREEN}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                  PACKAGE CREATION COMPLETE                  ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

success "Deployment package created successfully!"
log "Package location: $PACKAGE_DIR"
log "Compressed packages:"
log "  - $PACKAGE_DIR.tar.gz"
log "  - $PACKAGE_DIR.zip"

echo -e "\n${YELLOW}Next Steps:${NC}"
echo "1. Upload the package to your production server"
echo "2. Extract the package"
echo "3. Follow the instructions in deployment/README.md"
echo "4. Configure environment variables"
echo "5. Run the deployment script"

echo -e "\n${BLUE}Package Contents:${NC}"
echo "- Complete application source code"
echo "- Deployment scripts and automation"
echo "- Configuration templates"
echo "- Documentation and guides"
echo "- Health check and diagnostic tools"

log "Package creation completed successfully"
