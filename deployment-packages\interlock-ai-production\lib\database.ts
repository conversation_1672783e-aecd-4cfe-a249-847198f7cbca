import { prisma } from './prisma'

// Type definitions that match our Prisma models
export interface ResearchArea {
  id: number
  title: string
  summary: string
  lead: string
  last_updated: Date
  status: string
  category: string
  created_at: Date
}

export interface FeatureCard {
  id: number
  title: string
  description: string
  icon: string
  image?: string | null
  order_index: number
  created_at: Date
  updated_at: Date
}

export interface TeamMember {
  id: number
  name: string
  position: string
  bio: string
  image?: string | null
  initials: string
  order_index: number
  is_active: boolean
  created_at: Date
  updated_at: Date
}

export interface JobPosting {
  id: number
  title: string
  department: string
  location: string
  type: string
  description: string
  requirements: string[]
  is_active: boolean
  posted_date: Date
  created_at: Date
  updated_at: Date
}

export interface CompanyInfo {
  id: number
  section: string
  title: string
  content: string
  image?: string | null
  order_index: number
  last_updated: Date
  created_at: Date
}

export interface PageContent {
  id: number
  page_id: string
  section_id: string
  title: string
  content: string
  image?: string | null
  order_index: number
  is_active: boolean
  last_updated: Date
  created_at: Date
}

// Research Area CRUD operations
export async function getResearchAreas(): Promise<ResearchArea[]> {
  return await prisma.research_areas.findMany({
    orderBy: { created_at: 'desc' }
  })
}

export async function getResearchAreaById(id: number): Promise<ResearchArea | null> {
  return await prisma.research_areas.findUnique({
    where: { id }
  })
}

export async function getResearchAreasByFilter(filter: string): Promise<ResearchArea[]> {
  if (filter === "all") {
    return await prisma.research_areas.findMany({
      orderBy: { created_at: 'desc' }
    })
  }

  return await prisma.research_areas.findMany({
    where: {
      OR: [
        { category: filter },
        { status: filter }
      ]
    },
    orderBy: { created_at: 'desc' }
  })
}

export async function addResearchArea(data: Omit<ResearchArea, "id" | "created_at" | "last_updated">): Promise<ResearchArea> {
  return await prisma.research_areas.create({
    data: {
      title: data.title,
      summary: data.summary,
      lead: data.lead,
      status: data.status,
      category: data.category,
    }
  })
}

export async function updateResearchArea(id: number, data: Partial<Omit<ResearchArea, "id" | "created_at">>): Promise<ResearchArea | null> {
  try {
    return await prisma.research_areas.update({
      where: { id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.summary && { summary: data.summary }),
        ...(data.lead && { lead: data.lead }),
        ...(data.status && { status: data.status }),
        ...(data.category && { category: data.category }),
      }
    })
  } catch (error) {
    return null
  }
}

export async function deleteResearchArea(id: number): Promise<boolean> {
  try {
    await prisma.research_areas.delete({
      where: { id }
    })
    return true
  } catch (error) {
    return false
  }
}

// Feature Card CRUD operations
export async function getFeatureCards(): Promise<FeatureCard[]> {
  return await prisma.feature_cards.findMany({
    orderBy: { order_index: 'asc' }
  })
}

export async function getFeatureCardById(id: number): Promise<FeatureCard | null> {
  return await prisma.feature_cards.findUnique({
    where: { id }
  })
}

export async function addFeatureCard(data: Omit<FeatureCard, "id" | "created_at" | "updated_at">): Promise<FeatureCard> {
  try {
    console.log("Adding feature card:", data)
    const result = await prisma.feature_cards.create({
      data: {
        title: data.title,
        description: data.description,
        icon: data.icon,
        image: data.image,
        order_index: data.order_index,
      }
    })
    console.log("Feature card created:", result)
    return result
  } catch (error) {
    console.error("Error adding feature card:", error)
    throw error
  }
}

export async function updateFeatureCard(id: number, data: Partial<Omit<FeatureCard, "id" | "created_at" | "updated_at">>): Promise<FeatureCard | null> {
  try {
    console.log("Updating feature card:", id, data)
    const result = await prisma.feature_cards.update({
      where: { id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.description && { description: data.description }),
        ...(data.icon && { icon: data.icon }),
        ...(data.image !== undefined && { image: data.image }),
        ...(data.order_index && { order_index: data.order_index }),
      }
    })
    console.log("Feature card updated:", result)
    return result
  } catch (error) {
    console.error("Error updating feature card:", error)
    return null
  }
}

export async function deleteFeatureCard(id: number): Promise<boolean> {
  try {
    await prisma.feature_cards.delete({
      where: { id }
    })
    return true
  } catch (error) {
    return false
  }
}

export async function reorderFeatureCards(orderedIds: number[]): Promise<FeatureCard[]> {
  // Update each card's order based on its position in the array
  await Promise.all(
    orderedIds.map((id, index) =>
      prisma.feature_cards.update({
        where: { id },
        data: { order_index: index + 1 }
      })
    )
  )

  return await getFeatureCards()
}

// Team Members CRUD operations
export async function getTeamMembers(): Promise<TeamMember[]> {
  return await prisma.team_members.findMany({
    where: { is_active: true },
    orderBy: { order_index: 'asc' }
  })
}

export async function getTeamMemberById(id: number): Promise<TeamMember | null> {
  return await prisma.team_members.findUnique({
    where: { id }
  })
}

export async function addTeamMember(data: Omit<TeamMember, "id" | "created_at" | "updated_at">): Promise<TeamMember> {
  return await prisma.team_members.create({
    data: {
      name: data.name,
      position: data.position,
      bio: data.bio,
      image: data.image,
      initials: data.initials,
      order_index: data.order_index,
      is_active: data.is_active,
    }
  })
}

export async function updateTeamMember(id: number, data: Partial<Omit<TeamMember, "id" | "created_at" | "updated_at">>): Promise<boolean> {
  try {
    await prisma.team_members.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.position && { position: data.position }),
        ...(data.bio && { bio: data.bio }),
        ...(data.image !== undefined && { image: data.image }),
        ...(data.initials && { initials: data.initials }),
        ...(data.order_index && { order_index: data.order_index }),
        ...(data.is_active !== undefined && { is_active: data.is_active }),
      }
    })
    return true
  } catch (error) {
    return false
  }
}

export async function deleteTeamMember(id: number): Promise<boolean> {
  try {
    await prisma.team_members.update({
      where: { id },
      data: { is_active: false }
    })
    return true
  } catch (error) {
    return false
  }
}

// Job Postings CRUD operations
export async function getJobPostings(): Promise<JobPosting[]> {
  return await prisma.job_postings.findMany({
    where: { is_active: true },
    orderBy: { posted_date: 'desc' }
  })
}

export async function getJobPostingById(id: number): Promise<JobPosting | null> {
  return await prisma.job_postings.findUnique({
    where: { id }
  })
}

export async function addJobPosting(data: Omit<JobPosting, "id" | "created_at" | "updated_at" | "posted_date">): Promise<JobPosting> {
  return await prisma.job_postings.create({
    data: {
      title: data.title,
      department: data.department,
      location: data.location,
      type: data.type,
      description: data.description,
      requirements: data.requirements,
      is_active: data.is_active,
    }
  })
}

export async function updateJobPosting(id: number, data: Partial<Omit<JobPosting, "id" | "created_at" | "updated_at" | "posted_date">>): Promise<boolean> {
  try {
    await prisma.job_postings.update({
      where: { id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.department && { department: data.department }),
        ...(data.location && { location: data.location }),
        ...(data.type && { type: data.type }),
        ...(data.description && { description: data.description }),
        ...(data.requirements && { requirements: data.requirements }),
        ...(data.is_active !== undefined && { is_active: data.is_active }),
      }
    })
    return true
  } catch (error) {
    return false
  }
}

export async function deleteJobPosting(id: number): Promise<boolean> {
  try {
    await prisma.job_postings.update({
      where: { id },
      data: { is_active: false }
    })
    return true
  } catch (error) {
    return false
  }
}

// Company Info CRUD operations
export async function getCompanyInfo(): Promise<CompanyInfo[]> {
  return await prisma.company_info.findMany({
    orderBy: { order_index: 'asc' }
  })
}

export async function getCompanyInfoById(id: number): Promise<CompanyInfo | null> {
  return await prisma.company_info.findUnique({
    where: { id }
  })
}

export async function addCompanyInfo(data: Omit<CompanyInfo, "id" | "created_at" | "last_updated">): Promise<CompanyInfo> {
  return await prisma.company_info.create({
    data: {
      section: data.section,
      title: data.title,
      content: data.content,
      image: data.image,
      order_index: data.order_index,
    }
  })
}

export async function updateCompanyInfo(id: number, data: Partial<Omit<CompanyInfo, "id" | "created_at" | "last_updated">>): Promise<boolean> {
  try {
    await prisma.company_info.update({
      where: { id },
      data: {
        ...(data.section && { section: data.section }),
        ...(data.title && { title: data.title }),
        ...(data.content && { content: data.content }),
        ...(data.image !== undefined && { image: data.image }),
        ...(data.order_index && { order_index: data.order_index }),
      }
    })
    return true
  } catch (error) {
    return false
  }
}

export async function deleteCompanyInfo(id: number): Promise<boolean> {
  try {
    await prisma.company_info.delete({
      where: { id }
    })
    return true
  } catch (error) {
    return false
  }
}

// Page Content CRUD operations
export async function getPageContent(pageId?: string): Promise<PageContent[]> {
  const where = pageId
    ? { page_id: pageId, is_active: true }
    : { is_active: true }

  return await prisma.page_content.findMany({
    where,
    orderBy: { order_index: 'asc' }
  })
}

export async function getPageContentById(id: number): Promise<PageContent | null> {
  return await prisma.page_content.findUnique({
    where: { id }
  })
}

export async function addPageContent(data: Omit<PageContent, "id" | "created_at" | "last_updated">): Promise<PageContent> {
  return await prisma.page_content.create({
    data: {
      page_id: data.page_id,
      section_id: data.section_id,
      title: data.title,
      content: data.content,
      image: data.image,
      order_index: data.order_index,
      is_active: data.is_active,
    }
  })
}

export async function updatePageContent(id: number, data: Partial<Omit<PageContent, "id" | "created_at" | "last_updated">>): Promise<boolean> {
  try {
    await prisma.page_content.update({
      where: { id },
      data: {
        ...(data.page_id && { page_id: data.page_id }),
        ...(data.section_id && { section_id: data.section_id }),
        ...(data.title && { title: data.title }),
        ...(data.content && { content: data.content }),
        ...(data.image !== undefined && { image: data.image }),
        ...(data.order_index && { order_index: data.order_index }),
        ...(data.is_active !== undefined && { is_active: data.is_active }),
      }
    })
    return true
  } catch (error) {
    return false
  }
}

export async function deletePageContent(id: number): Promise<boolean> {
  try {
    await prisma.page_content.update({
      where: { id },
      data: { is_active: false }
    })
    return true
  } catch (error) {
    return false
  }
}
