{"name": "interlock-ai", "version": "1.0.0", "description": "Interlock AI - Production Build", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "node server.js", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.10.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^11.0.8", "lucide-react": "^0.358.0", "next": "14.1.3", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.51.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8", "prisma": "^5.10.2", "tailwindcss": "^3.3.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}