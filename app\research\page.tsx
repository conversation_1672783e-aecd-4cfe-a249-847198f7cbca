"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { getResearchAreas, type ResearchArea } from "@/lib/api-client"
import { LoadingSpinner } from "@/components/loading-spinner"

// Mock research data - in a real app, this would come from the database
// const researchAreas = [
//   {
//     id: 1,
//     title: "Neural Associative Modeling",
//     summary:
//       "Creating AI systems that form associations between concepts similar to human cognitive processes, enabling more intuitive learning and reasoning.",
//     lead: "Dr. <PERSON>",
//     lastUpdated: "2024-04-15",
//     status: "in-progress",
//     category: "cognitive",
//   },
//   {
//     id: 2,
//     title: "Memory Graph Prioritization",
//     summary:
//       "Developing algorithms that prioritize information based on relevance, recency, and emotional significance, similar to human memory systems.",
//     lead: "Dr. <PERSON>",
//     lastUpdated: "2024-03-28",
//     status: "published",
//     category: "memory",
//   },
//   {
//     id: 3,
//     title: "Contextual Understanding Frameworks",
//     summary:
//       "Building systems that comprehend nuanced contexts and adapt responses accordingly, improving human-AI interactions.",
//     lead: "Dr. Sophia Rodriguez",
//     lastUpdated: "2024-04-02",
//     status: "published",
//     category: "cognitive",
//   },
//   {
//     id: 4,
//     title: "Emotional Intelligence Integration",
//     summary:
//       "Incorporating emotional intelligence into AI decision-making processes to enhance empathy and social awareness in automated systems.",
//     lead: "Dr. James Wilson",
//     lastUpdated: "2024-03-10",
//     status: "in-progress",
//     category: "emotional",
//   },
//   {
//     id: 5,
//     title: "Adaptive Learning Pathways",
//     summary:
//       "Creating systems that adjust learning strategies based on performance and engagement, similar to human metacognitive processes.",
//     lead: "Dr. Aisha Patel",
//     lastUpdated: "2024-04-08",
//     status: "published",
//     category: "learning",
//   },
//   {
//     id: 6,
//     title: "Intuitive Decision Making",
//     summary:
//       "Modeling human intuition and heuristics to develop AI systems that can make rapid, effective decisions with incomplete information.",
//     lead: "Dr. Thomas Nakamura",
//     lastUpdated: "2024-02-20",
//     status: "archived",
//     category: "decision",
//   },
// ]

export default function ResearchPage() {
  const [researchAreas, setResearchAreas] = useState<ResearchArea[]>([])
  const [filteredResearch, setFilteredResearch] = useState<ResearchArea[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState("all")

  useEffect(() => {
    async function loadResearch() {
      try {
        const data = await getResearchAreas()
        setResearchAreas(data)
        setFilteredResearch(data)
        setLoading(false)
      } catch (error) {
        console.error("Failed to load research:", error)
        setLoading(false)
      }
    }

    loadResearch()
  }, [])

  const handleFilterChange = (newFilter: string) => {
    setFilter(newFilter)

    if (newFilter === "all") {
      setFilteredResearch(researchAreas)
    } else {
      const filtered = researchAreas.filter(
        (item) => item.category === newFilter || item.status === newFilter
      )
      setFilteredResearch(filtered)
    }
  }

  // const [filter, setFilter] = useState("all")

  // const filteredResearch =
  //   filter === "all"
  //     ? researchAreas
  //     : researchAreas.filter((item) => item.category === filter || item.status === filter)

  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto mb-12 text-center"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Research Areas
        </h1>
        <p className="text-xl text-gray-300">
          Explore our cutting-edge research in human-inspired AI learning algorithms
        </p>
      </motion.div>

      <Tabs defaultValue="all" className="max-w-5xl mx-auto mb-8">
        <TabsList className="grid grid-cols-3 md:grid-cols-6 mb-8" aria-label="Filter research by category">
          <TabsTrigger value="all" onClick={() => handleFilterChange("all")}>
            All
          </TabsTrigger>
          <TabsTrigger value="cognitive" onClick={() => handleFilterChange("cognitive")}>
            Cognitive
          </TabsTrigger>
          <TabsTrigger value="memory" onClick={() => handleFilterChange("memory")}>
            Memory
          </TabsTrigger>
          <TabsTrigger value="emotional" onClick={() => handleFilterChange("emotional")}>
            Emotional
          </TabsTrigger>
          <TabsTrigger value="learning" onClick={() => handleFilterChange("learning")}>
            Learning
          </TabsTrigger>
          <TabsTrigger value="decision" onClick={() => handleFilterChange("decision")}>
            Decision
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {loading ? (
        <LoadingSpinner />
      ) : filteredResearch.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {filteredResearch.map((research, index) => (
            <ResearchCard key={research.id} research={research} index={index} />
          ))}
        </div>
      ) : (
        <div className="col-span-full text-center py-12">
          <p className="text-gray-400">No research found for this filter.</p>
        </div>
      )}
    </div>
  )
}

function ResearchCard({ research, index }: { research: any; index: number }) {
  const statusColors = {
    published: "bg-green-500",
    "in-progress": "bg-blue-500",
    archived: "bg-gray-500",
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <Card
        className="h-full border-purple-500/20 bg-gray-900/50 backdrop-blur-sm hover:border-purple-500/50 transition-all duration-300 shadow-[0_0_15px_rgba(124,58,237,0.1)] hover:shadow-[0_0_25px_rgba(124,58,237,0.2)]"
        tabIndex={0}
      >
        <CardHeader>
          <div className="flex justify-between items-start">
            <Badge
              variant="outline"
              className={`${statusColors[research.status as keyof typeof statusColors]} text-white`}
            >
              {research.status.charAt(0).toUpperCase() + research.status.slice(1)}
            </Badge>
            <Badge variant="outline" className="bg-purple-500/20 text-purple-300 hover:bg-purple-500/30">
              {research.category.charAt(0).toUpperCase() + research.category.slice(1)}
            </Badge>
          </div>
          <CardTitle className="text-xl text-cyan-400 mt-2">{research.title}</CardTitle>
          <CardDescription className="text-gray-400">Led by {research.lead}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-300">{research.summary}</p>
        </CardContent>
        <CardFooter className="text-sm text-gray-400 flex justify-between">
          <span>Last updated: {research.lastUpdated}</span>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="text-cyan-400 hover:text-cyan-300 transition-colors"
            aria-label={`Read more about ${research.title}`}
          >
            Read more →
          </motion.button>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
